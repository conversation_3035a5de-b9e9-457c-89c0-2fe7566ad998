#!/usr/bin/env python3
"""
订阅页面自动化脚本 - 专门用于访问Augment Code订阅页面
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Chrome路径
CHROME_PATH = r"E:\Google\Chrome\Application\chrome.exe"

class SubscriptionBot:
    def __init__(self, headless=False):
        """初始化订阅页面机器人"""
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置Chrome WebDriver - 无痕模式"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")
            
            # 直接指定Chrome路径
            chrome_options.binary_location = CHROME_PATH
            
            # 启用无痕模式
            chrome_options.add_argument("--incognito")
            
            # 每次启动新的浏览器实例
            chrome_options.add_argument("--new-window")
            
            # 添加其他选项
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 禁用保存密码提示和其他弹窗
            chrome_options.add_experimental_option("prefs", {
                "credentials_enable_service": False,
                "profile.password_manager_enabled": False,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0
            })
            
            # 用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # 获取ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 15)
            
            # 最大化窗口
            self.driver.maximize_window()
            
            logger.info("Chrome无痕模式 WebDriver 初始化成功")
            
        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def navigate_to_subscription(self, url="https://app.augmentcode.com/account/subscription"):
        """导航到订阅页面"""
        try:
            logger.info(f"正在访问订阅页面: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 检查页面是否加载成功
            page_title = self.driver.title
            logger.info(f"页面标题: {page_title}")
            
            # 检查是否需要登录
            if "login" in self.driver.current_url.lower() or "sign" in self.driver.current_url.lower():
                logger.warning("页面重定向到登录页面，可能需要先登录")
                return "需要登录"
            
            logger.info("订阅页面访问成功")
            return "成功"
            
        except Exception as e:
            logger.error(f"无法访问订阅页面: {e}")
            return "失败"
    
    def wait_for_user_interaction(self, duration=30):
        """等待用户手动操作"""
        logger.info(f"等待用户操作 {duration} 秒...")
        time.sleep(duration)
    
    def take_screenshot(self, filename="subscription_page.png"):
        """截取当前页面截图"""
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"截图已保存: {filename}")
            return True
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False
    
    def get_page_info(self):
        """获取当前页面信息"""
        try:
            info = {
                "title": self.driver.title,
                "url": self.driver.current_url,
                "page_source_length": len(self.driver.page_source)
            }
            logger.info(f"页面信息: {info}")
            return info
        except Exception as e:
            logger.error(f"获取页面信息失败: {e}")
            return None
    
    def find_form_elements(self):
        """查找页面中的表单元素"""
        try:
            form_elements = {
                "inputs": [],
                "buttons": [],
                "links": []
            }

            # 查找所有输入框
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            for input_elem in inputs:
                if input_elem.is_displayed():
                    form_elements["inputs"].append({
                        "type": input_elem.get_attribute("type"),
                        "name": input_elem.get_attribute("name"),
                        "id": input_elem.get_attribute("id"),
                        "placeholder": input_elem.get_attribute("placeholder"),
                        "class": input_elem.get_attribute("class")
                    })

            # 查找所有按钮
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            for button in buttons:
                if button.is_displayed():
                    form_elements["buttons"].append({
                        "text": button.text,
                        "type": button.get_attribute("type"),
                        "class": button.get_attribute("class"),
                        "id": button.get_attribute("id")
                    })

            # 查找所有链接
            links = self.driver.find_elements(By.TAG_NAME, "a")
            for link in links:
                if link.is_displayed() and link.text:
                    href = link.get_attribute("href")
                    if href and ("register" in href.lower() or "sign" in href.lower()):
                        form_elements["links"].append({
                            "text": link.text,
                            "href": href,
                            "class": link.get_attribute("class")
                        })

            logger.info(f"找到表单元素: {len(form_elements['inputs'])} 个输入框, {len(form_elements['buttons'])} 个按钮, {len(form_elements['links'])} 个相关链接")
            return form_elements

        except Exception as e:
            logger.error(f"查找表单元素失败: {e}")
            return {"inputs": [], "buttons": [], "links": []}

    def auto_fill_login_form(self, email, password):
        """自动填写登录表单"""
        try:
            logger.info("开始自动填写登录表单...")

            # 等待页面加载
            time.sleep(2)

            # 查找邮箱输入框
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[id='email']",
                "input[placeholder*='email']",
                "input[placeholder*='Email']",
                "input[name='username']",
                "input[id='username']"
            ]

            email_filled = False
            for selector in email_selectors:
                try:
                    email_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if email_field.is_displayed():
                        email_field.clear()
                        self.type_slowly(email_field, email)
                        logger.info(f"邮箱填写成功: {selector}")
                        email_filled = True
                        break
                except:
                    continue

            if not email_filled:
                logger.warning("未找到邮箱输入框")
                return False

            # 查找密码输入框
            password_selectors = [
                "input[type='password']",
                "input[name='password']",
                "input[id='password']"
            ]

            password_filled = False
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if password_field.is_displayed():
                        password_field.clear()
                        self.type_slowly(password_field, password)
                        logger.info(f"密码填写成功: {selector}")
                        password_filled = True
                        break
                except:
                    continue

            if not password_filled:
                logger.warning("未找到密码输入框")
                return False

            return True

        except Exception as e:
            logger.error(f"自动填写登录表单失败: {e}")
            return False

    def auto_fill_register_form(self, email, password, username=None, confirm_password=None):
        """自动填写注册表单"""
        try:
            logger.info("开始自动填写注册表单...")

            # 等待页面加载
            time.sleep(2)

            # 如果没有提供确认密码，使用相同密码
            if not confirm_password:
                confirm_password = password

            # 填写邮箱
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[id='email']",
                "input[placeholder*='email']",
                "input[placeholder*='Email']"
            ]

            if not self.fill_field_by_selectors(email_selectors, email, "邮箱"):
                return False

            # 填写用户名（如果需要）
            if username:
                username_selectors = [
                    "input[name='username']",
                    "input[id='username']",
                    "input[placeholder*='username']",
                    "input[placeholder*='Username']",
                    "input[name='name']",
                    "input[id='name']"
                ]
                self.fill_field_by_selectors(username_selectors, username, "用户名")

            # 填写密码
            password_selectors = [
                "input[type='password'][name='password']",
                "input[type='password'][id='password']",
                "input[type='password']:first-of-type"
            ]

            if not self.fill_field_by_selectors(password_selectors, password, "密码"):
                return False

            # 填写确认密码
            confirm_password_selectors = [
                "input[name='confirmPassword']",
                "input[name='confirm_password']",
                "input[name='passwordConfirm']",
                "input[id='confirmPassword']",
                "input[id='confirm_password']",
                "input[placeholder*='confirm']",
                "input[placeholder*='Confirm']",
                "input[type='password']:last-of-type"
            ]

            self.fill_field_by_selectors(confirm_password_selectors, confirm_password, "确认密码")

            return True

        except Exception as e:
            logger.error(f"自动填写注册表单失败: {e}")
            return False

    def fill_field_by_selectors(self, selectors, value, field_name):
        """通过多个选择器尝试填写字段"""
        for selector in selectors:
            try:
                field = self.driver.find_element(By.CSS_SELECTOR, selector)
                if field.is_displayed():
                    field.clear()
                    self.type_slowly(field, value)
                    logger.info(f"{field_name}填写成功: {selector}")
                    return True
            except:
                continue

        logger.warning(f"未找到{field_name}输入框")
        return False

    def type_slowly(self, element, text, delay_range=(0.05, 0.15)):
        """模拟人类打字速度"""
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(*delay_range))

    def click_submit_button(self):
        """点击提交按钮"""
        try:
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button:contains('Sign in')",
                "button:contains('Login')",
                "button:contains('Register')",
                "button:contains('Sign up')",
                ".btn-primary",
                ".submit-btn",
                ".login-btn",
                ".register-btn"
            ]

            for selector in submit_selectors:
                try:
                    if ":contains(" in selector:
                        # 使用XPath处理包含文本的选择器
                        text = selector.split("'")[1]
                        xpath = f"//button[contains(text(), '{text}')]"
                        button = self.driver.find_element(By.XPATH, xpath)
                    else:
                        button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if button.is_displayed():
                        self.driver.execute_script("arguments[0].scrollIntoView();", button)
                        time.sleep(1)
                        button.click()
                        logger.info(f"提交按钮点击成功: {selector}")
                        return True
                except:
                    continue

            logger.warning("未找到提交按钮")
            return False

        except Exception as e:
            logger.error(f"点击提交按钮失败: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("=== Augment Code 订阅页面自动化 ===\n")
    
    # 检查Chrome是否存在
    if not os.path.exists(CHROME_PATH):
        print(f"❌ Chrome未找到: {CHROME_PATH}")
        return
    
    print(f"✅ Chrome找到: {CHROME_PATH}")
    
    # 创建机器人
    bot = SubscriptionBot(headless=False)  # 设置为False以查看过程
    
    try:
        # 访问订阅页面
        result = bot.navigate_to_subscription()
        
        if result == "成功":
            print("✅ 订阅页面访问成功")
            
            # 获取页面信息
            page_info = bot.get_page_info()
            
            # 查找订阅相关元素
            subscription_elements = bot.find_subscription_elements()
            
            # 截图
            bot.take_screenshot("augment_subscription_page.png")
            
            # 等待用户操作
            print("\n浏览器将保持打开状态30秒，您可以手动操作...")
            bot.wait_for_user_interaction(30)
            
        elif result == "需要登录":
            print("⚠️  页面重定向到登录页面，请先登录")
            print("浏览器将保持打开状态60秒，请手动登录...")
            bot.wait_for_user_interaction(60)
            
        else:
            print("❌ 访问订阅页面失败")
            
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        
    finally:
        print("\n正在关闭浏览器...")
        bot.close()

if __name__ == "__main__":
    main()
