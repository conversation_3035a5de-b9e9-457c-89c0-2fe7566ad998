# 指纹浏览器管理系统

基于比特浏览器API的完整指纹浏览器管理解决方案，支持多账号管理、代理轮换、指纹随机化等功能。

## 🌟 主要特性

- **指纹随机化**: 自动生成随机的浏览器指纹，包括WebGL、Canvas、User Agent等
- **代理管理**: 支持HTTP/HTTPS/SOCKS5代理，可批量配置和轮换
- **多账号管理**: 支持创建和管理多个浏览器配置文件
- **批量操作**: 支持批量创建、打开、关闭、删除浏览器
- **窗口排列**: 自动排列浏览器窗口，支持宫格和对角线排列
- **分组管理**: 支持浏览器分组，便于管理不同项目
- **配置持久化**: 自动保存和加载浏览器配置
- **交互式界面**: 提供命令行界面，操作简单直观

## 📋 系统要求

### 必需软件
1. **比特浏览器**: 从 [官网](https://www.bitbrowser.cn/) 下载并安装
2. **Python 3.7+**: 确保已安装Python
3. **Chrome浏览器**: 用于WebDriver连接

### Python依赖
```bash
pip install selenium requests faker
```

## 🚀 快速开始

### 1. 安装比特浏览器
- 下载并安装比特浏览器
- 启动比特浏览器客户端
- 确保比特浏览器正在运行（系统会自动检测端口）

### 2. 运行交互式界面
```bash
python browser_cli.py
```

### 3. 运行示例代码
```bash
python fingerprint_browser_examples.py
```

## 📖 使用指南

### 基础使用

#### 1. 创建简单浏览器
```python
from fingerprint_browser_manager import FingerprintBrowserManager

# 创建管理器
manager = FingerprintBrowserManager("MyProject")

# 配置浏览器
config = {
    'username': 'test_user',
    'password': 'test_pass',
    'group': 'test_group',
    'url': 'https://www.google.com'
}

# 创建浏览器
browser_id = manager.create_browser_profile('TestBrowser', config)

# 打开浏览器
driver = manager.open_browser('TestBrowser')

# 使用WebDriver进行自动化
if driver:
    print(f"当前页面: {driver.title}")
    # 添加你的自动化逻辑...
    
    # 关闭浏览器
    manager.close_browser('TestBrowser')
```

#### 2. 使用代理
```python
config = {
    'username': 'proxy_user',
    'password': 'proxy_pass',
    'group': 'proxy_group',
    'url': 'https://httpbin.org/ip',
    'proxy': {
        'type': 'http',
        'host': '127.0.0.1',
        'port': '8080',
        'username': 'proxy_user',
        'password': 'proxy_pass'
    }
}

browser_id = manager.create_browser_profile('ProxyBrowser', config)
```

#### 3. 自定义指纹
```python
config = {
    'username': 'fingerprint_user',
    'password': 'fingerprint_pass',
    'group': 'fingerprint_group',
    'url': 'https://www.whatismybrowser.com',
    'fingerprint': {
        'resolution': '1366x768',
        'timeZone': 'Asia/Shanghai',
        'languages': 'zh-CN,zh',
        'webGL': '0',           # 启用WebGL指纹随机化
        'canvas': '0',          # 启用Canvas指纹随机化
        'hardwareConcurrency': '4',
        'deviceMemory': '8'
    }
}

browser_id = manager.create_browser_profile('FingerprintBrowser', config)
```

### 批量操作

#### 批量创建浏览器
```python
base_config = {
    'group': 'batch_test',
    'url': 'https://www.google.com'
}

for i in range(1, 11):  # 创建10个浏览器
    name = f"Browser_{i:03d}"
    config = base_config.copy()
    config.update({
        'username': f'user_{i:03d}',
        'password': f'pass_{i:03d}'
    })
    
    browser_id = manager.create_browser_profile(name, config)
```

#### 批量打开浏览器
```python
# 打开指定分组的所有浏览器
for name, info in manager.configs.items():
    if info['config'].get('group') == 'batch_test':
        manager.open_browser(name)
        time.sleep(1)  # 避免同时打开太多

# 排列窗口
manager.arrange_windows('box', columns=3)
```

### 窗口管理

#### 排列窗口
```python
# 宫格排列
manager.arrange_windows('box', columns=3)

# 对角线排列
manager.arrange_windows('diagonal')
```

### 代理管理

#### 更新浏览器代理
```python
new_proxy = {
    'type': 'socks5',
    'host': '127.0.0.1',
    'port': '1080',
    'username': '',
    'password': ''
}

manager.update_browser_proxy('BrowserName', new_proxy)
```

#### 禁用代理
```python
no_proxy = {
    'type': 'noproxy',
    'host': '',
    'port': '',
    'username': '',
    'password': ''
}

manager.update_browser_proxy('BrowserName', no_proxy)
```

## 🔧 配置说明

### 浏览器配置参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| username | string | 用户名 | "test_user" |
| password | string | 密码 | "test_pass" |
| group | string | 分组名称 | "test_group" |
| url | string | 启动URL | "https://www.google.com" |
| proxy | object | 代理配置 | 见代理配置 |
| fingerprint | object | 指纹配置 | 见指纹配置 |

### 代理配置参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| type | string | 代理类型 | "http", "https", "socks5", "noproxy" |
| host | string | 代理主机 | "127.0.0.1" |
| port | string | 代理端口 | "8080" |
| username | string | 代理用户名 | "proxy_user" |
| password | string | 代理密码 | "proxy_pass" |

### 指纹配置参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| resolution | string | 屏幕分辨率 | "1920x1080" |
| timeZone | string | 时区 | "Asia/Shanghai" |
| languages | string | 语言 | "zh-CN,zh" |
| webGL | string | WebGL指纹 | "0" (随机化) |
| canvas | string | Canvas指纹 | "0" (随机化) |
| hardwareConcurrency | string | CPU核心数 | "4" |
| deviceMemory | string | 设备内存 | "8" |

## 🎯 实际应用场景

### 1. 多账号管理
- 社交媒体账号管理
- 电商平台多店铺运营
- 广告投放账号管理

### 2. 数据采集
- 网站数据爬取
- 价格监控
- 竞品分析

### 3. 自动化测试
- Web应用测试
- 兼容性测试
- 性能测试

### 4. 隐私保护
- 匿名浏览
- 防止追踪
- 地理位置伪装

## 🛠️ 高级功能

### 集成现有自动化脚本
```python
# 结合之前的注册脚本
from fingerprint_browser_manager import FingerprintBrowserManager

manager = FingerprintBrowserManager("Registration_Project")

# 创建专用浏览器
config = {
    'username': '<EMAIL>',
    'password': 'your_password',
    'group': 'registration',
    'url': 'https://app.augmentcode.com/account/subscription',
    'fingerprint': {
        'resolution': '1920x1080',
        'timeZone': 'America/New_York',
        'languages': 'en-US'
    }
}

browser_id = manager.create_browser_profile('RegistrationBrowser', config)
driver = manager.open_browser('RegistrationBrowser')

# 在这里添加你的注册逻辑
# 使用driver进行自动化操作...
```

### 自定义指纹生成
```python
from bit_browser_api import BitBrowserAPI

api = BitBrowserAPI("CustomProject")

# 创建完全自定义的浏览器
custom_config = {
    'name': 'CustomBrowser',
    'username': 'custom_user',
    'password': 'custom_pass',
    'resolution': '1366x768',
    'timeZone': 'Europe/London',
    'languages': 'en-GB',
    'webGLManufacturer': 'Intel Inc.',
    'webGLRender': 'Intel(R) HD Graphics 620',
    'hardwareConcurrency': '4',
    'deviceMemory': '8'
}

browser_id = api.create_browser(**custom_config)
```

## 🚨 注意事项

1. **合法使用**: 请确保在合法范围内使用本工具
2. **资源管理**: 及时关闭不需要的浏览器实例
3. **代理质量**: 使用高质量的代理服务
4. **请求频率**: 避免过于频繁的请求
5. **数据备份**: 定期备份浏览器配置文件

## 🔍 故障排除

### 常见问题

1. **比特浏览器连接失败**
   - 确保比特浏览器正在运行
   - 检查防火墙设置
   - 重启比特浏览器

2. **浏览器打开失败**
   - 检查Chrome浏览器是否安装
   - 更新ChromeDriver
   - 检查系统资源

3. **代理连接失败**
   - 验证代理服务器状态
   - 检查代理认证信息
   - 测试代理连接

4. **指纹检测失败**
   - 更新指纹参数
   - 使用不同的指纹组合
   - 检查目标网站的检测机制

## 📞 技术支持

如果遇到问题，请检查：
1. 比特浏览器是否正常运行
2. Python依赖是否正确安装
3. 网络连接是否正常
4. 代理配置是否正确

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
