#!/usr/bin/env python3
"""
智能自动注册脚本
使用配置文件管理用户信息和网站配置
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 导入配置
from user_config import (
    USER_INFO, WEBSITE_URLS, BROWSER_CONFIG, 
    FORM_SELECTORS, BUTTON_SELECTORS, LINK_SELECTORS,
    validate_config, print_config_status
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SmartAutoRegisterBot:
    def __init__(self, headless=None):
        """初始化智能自动注册机器人"""
        self.driver = None
        self.wait = None
        
        # 使用配置文件中的设置
        if headless is None:
            headless = BROWSER_CONFIG["headless"]
            
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")
            
            # 使用配置文件中的Chrome路径
            chrome_options.binary_location = BROWSER_CONFIG["chrome_path"]
            
            # 根据配置启用无痕模式
            if BROWSER_CONFIG["incognito"]:
                chrome_options.add_argument("--incognito")
                chrome_options.add_argument("--new-window")
            
            # 标准选项
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 禁用各种弹窗
            chrome_options.add_experimental_option("prefs", {
                "credentials_enable_service": False,
                "profile.password_manager_enabled": False,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0
            })
            
            # 用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # 获取ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, BROWSER_CONFIG["wait_timeout"])
            
            # 最大化窗口
            self.driver.maximize_window()
            
            logger.info("Chrome WebDriver 初始化成功")
            
        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def navigate_to_page(self, url):
        """导航到指定页面"""
        try:
            logger.info(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(3)
            
            page_title = self.driver.title
            current_url = self.driver.current_url
            logger.info(f"页面标题: {page_title}")
            logger.info(f"当前URL: {current_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"无法访问页面: {e}")
            return False
    
    def type_slowly(self, element, text):
        """模拟人类打字速度"""
        element.clear()
        delay_range = BROWSER_CONFIG["typing_delay"]
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(*delay_range))
    
    def fill_field_by_selectors(self, selectors, value, field_name):
        """通过多个选择器尝试填写字段"""
        for selector in selectors:
            try:
                field = self.driver.find_element(By.CSS_SELECTOR, selector)
                if field.is_displayed():
                    self.type_slowly(field, value)
                    logger.info(f"{field_name}填写成功: {selector}")
                    return True
            except:
                continue
        
        logger.warning(f"未找到{field_name}输入框")
        return False
    
    def click_button_by_selectors(self, selectors, button_name):
        """通过多个选择器尝试点击按钮"""
        for selector in selectors:
            try:
                if selector.startswith("//"):
                    button = self.driver.find_element(By.XPATH, selector)
                else:
                    button = self.driver.find_element(By.CSS_SELECTOR, selector)
                
                if button.is_displayed():
                    self.driver.execute_script("arguments[0].scrollIntoView();", button)
                    time.sleep(1)
                    button.click()
                    logger.info(f"{button_name}按钮点击成功: {selector}")
                    return True
            except:
                continue
        
        logger.warning(f"未找到{button_name}按钮")
        return False
    
    def find_and_click_link(self, link_type):
        """查找并点击指定类型的链接"""
        try:
            selectors = LINK_SELECTORS.get(link_type, [])
            
            for selector in selectors:
                try:
                    if selector.startswith("//"):
                        link = self.driver.find_element(By.XPATH, selector)
                    else:
                        link = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if link.is_displayed():
                        link.click()
                        logger.info(f"{link_type}链接点击成功: {selector}")
                        time.sleep(3)
                        return True
                except:
                    continue
            
            logger.warning(f"未找到{link_type}链接")
            return False
            
        except Exception as e:
            logger.error(f"查找{link_type}链接失败: {e}")
            return False
    
    def auto_register(self):
        """自动注册流程"""
        try:
            logger.info("开始自动注册流程...")
            
            # 1. 访问注册页面
            if not self.navigate_to_page(WEBSITE_URLS["register"]):
                return False
            
            # 2. 检查是否在注册页面，如果不是则查找注册链接
            if "register" not in self.driver.current_url.lower() and "sign" not in self.driver.current_url.lower():
                logger.info("尝试查找注册链接...")
                if not self.find_and_click_link("register"):
                    logger.error("无法找到注册页面")
                    return False
            
            # 3. 填写注册表单
            success_count = 0
            
            # 填写邮箱
            if self.fill_field_by_selectors(FORM_SELECTORS["email"], USER_INFO["email"], "邮箱"):
                success_count += 1
            
            # 填写用户名（如果提供）
            if USER_INFO["username"]:
                if self.fill_field_by_selectors(FORM_SELECTORS["username"], USER_INFO["username"], "用户名"):
                    success_count += 1
            
            # 填写名字（如果提供）
            if USER_INFO["first_name"]:
                self.fill_field_by_selectors(FORM_SELECTORS["first_name"], USER_INFO["first_name"], "名字")
            
            # 填写姓氏（如果提供）
            if USER_INFO["last_name"]:
                self.fill_field_by_selectors(FORM_SELECTORS["last_name"], USER_INFO["last_name"], "姓氏")
            
            # 填写密码
            if self.fill_field_by_selectors(FORM_SELECTORS["password"], USER_INFO["password"], "密码"):
                success_count += 1
            
            # 填写确认密码
            confirm_password = USER_INFO["confirm_password"] or USER_INFO["password"]
            if self.fill_field_by_selectors(FORM_SELECTORS["confirm_password"], confirm_password, "确认密码"):
                success_count += 1
            
            # 填写其他字段（如果提供）
            if USER_INFO["phone"]:
                self.fill_field_by_selectors(FORM_SELECTORS["phone"], USER_INFO["phone"], "电话")
            
            if USER_INFO["company"]:
                self.fill_field_by_selectors(FORM_SELECTORS["company"], USER_INFO["company"], "公司")
            
            # 检查是否成功填写了关键字段
            if success_count < 2:
                logger.error("关键字段填写失败，无法继续")
                return False
            
            # 4. 点击注册按钮
            if self.click_button_by_selectors(BUTTON_SELECTORS["register"], "注册"):
                logger.info("注册表单提交成功")
                time.sleep(5)  # 等待处理
                return True
            else:
                logger.error("注册按钮点击失败")
                return False
            
        except Exception as e:
            logger.error(f"自动注册失败: {e}")
            return False
    
    def auto_login(self):
        """自动登录流程"""
        try:
            logger.info("开始自动登录流程...")
            
            # 1. 访问登录页面
            if not self.navigate_to_page(WEBSITE_URLS["login"]):
                return False
            
            # 2. 检查是否在登录页面
            if "login" not in self.driver.current_url.lower() and "sign" not in self.driver.current_url.lower():
                logger.info("尝试查找登录链接...")
                if not self.find_and_click_link("login"):
                    logger.error("无法找到登录页面")
                    return False
            
            # 3. 填写登录表单
            # 填写邮箱
            if not self.fill_field_by_selectors(FORM_SELECTORS["email"], USER_INFO["email"], "邮箱"):
                return False
            
            # 填写密码
            if not self.fill_field_by_selectors(FORM_SELECTORS["password"], USER_INFO["password"], "密码"):
                return False
            
            # 4. 点击登录按钮
            if self.click_button_by_selectors(BUTTON_SELECTORS["login"], "登录"):
                logger.info("登录表单提交成功")
                time.sleep(5)  # 等待处理
                return True
            else:
                logger.error("登录按钮点击失败")
                return False
            
        except Exception as e:
            logger.error(f"自动登录失败: {e}")
            return False
    
    def take_screenshot(self, filename):
        """截取屏幕截图"""
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"截图已保存: {filename}")
            return True
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False
    
    def wait_for_user(self, seconds=30):
        """等待用户操作"""
        logger.info(f"等待用户操作 {seconds} 秒...")
        time.sleep(seconds)
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("=== 智能自动注册脚本 ===\n")
    
    # 检查配置
    if not print_config_status():
        print("\n请先修改 user_config.py 文件中的配置信息！")
        return
    
    # 选择操作
    print("\n请选择操作：")
    print("1. 自动注册")
    print("2. 自动登录")
    print("3. 登录后访问订阅页面")
    print("4. 仅打开浏览器（手动操作）")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    bot = SmartAutoRegisterBot()
    
    try:
        if choice == "1":
            # 自动注册
            print("\n开始自动注册...")
            success = bot.auto_register()
            
            if success:
                print("✅ 注册流程完成")
                bot.take_screenshot("register_result.png")
                bot.wait_for_user(30)
            else:
                print("❌ 注册失败")
                bot.take_screenshot("register_error.png")
                
        elif choice == "2":
            # 自动登录
            print("\n开始自动登录...")
            success = bot.auto_login()
            
            if success:
                print("✅ 登录流程完成")
                bot.take_screenshot("login_result.png")
                bot.wait_for_user(30)
            else:
                print("❌ 登录失败")
                bot.take_screenshot("login_error.png")
                
        elif choice == "3":
            # 登录后访问订阅页面
            print("\n开始登录...")
            login_success = bot.auto_login()
            
            if login_success:
                print("✅ 登录成功，正在访问订阅页面...")
                time.sleep(3)
                bot.navigate_to_page(WEBSITE_URLS["subscription"])
                bot.take_screenshot("subscription_page.png")
                print("✅ 订阅页面访问完成")
                bot.wait_for_user(60)
            else:
                print("❌ 登录失败，无法访问订阅页面")
                
        elif choice == "4":
            # 仅打开浏览器
            print("\n打开浏览器，您可以手动操作...")
            bot.navigate_to_page(WEBSITE_URLS["home"])
            bot.wait_for_user(300)  # 等待5分钟
        
        else:
            print("❌ 无效选择")
            
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        bot.take_screenshot("error.png")
        
    finally:
        input("\n按回车键关闭浏览器...")
        bot.close()

if __name__ == "__main__":
    main()
