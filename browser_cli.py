#!/usr/bin/env python3
"""
指纹浏览器命令行界面
提供交互式的浏览器管理功能
"""

import sys
import time
from fingerprint_browser_manager import FingerprintBrowserManager

class BrowserCLI:
    def __init__(self):
        """初始化命令行界面"""
        self.manager = FingerprintBrowserManager("CLI_Project")
        self.running = True
    
    def show_banner(self):
        """显示欢迎横幅"""
        print("=" * 70)
        print("🌐 指纹浏览器管理系统")
        print("基于比特浏览器API的完整管理解决方案")
        print("=" * 70)
    
    def show_menu(self):
        """显示主菜单"""
        print("\n📋 主菜单:")
        print("1. 创建浏览器配置")
        print("2. 打开浏览器")
        print("3. 关闭浏览器")
        print("4. 删除浏览器")
        print("5. 列出所有浏览器")
        print("6. 批量操作")
        print("7. 窗口管理")
        print("8. 代理管理")
        print("9. 快速创建示例")
        print("0. 退出")
        print("-" * 40)
    
    def create_browser_interactive(self):
        """交互式创建浏览器"""
        print("\n🔧 创建新的浏览器配置")
        print("-" * 40)
        
        # 基本信息
        name = input("浏览器名称: ").strip()
        if not name:
            print("❌ 浏览器名称不能为空")
            return
        
        username = input("用户名 (可选): ").strip()
        password = input("密码 (可选): ").strip()
        group = input("分组名称 (默认: default): ").strip() or "default"
        url = input("启动URL (可选): ").strip()
        
        # 代理配置
        print("\n🌐 代理配置:")
        use_proxy = input("是否使用代理? (y/n): ").strip().lower() == 'y'
        proxy_config = None
        
        if use_proxy:
            proxy_type = input("代理类型 (http/https/socks5): ").strip() or "http"
            proxy_host = input("代理主机: ").strip()
            proxy_port = input("代理端口: ").strip()
            proxy_username = input("代理用户名 (可选): ").strip()
            proxy_password = input("代理密码 (可选): ").strip()
            
            if proxy_host and proxy_port:
                proxy_config = {
                    'type': proxy_type,
                    'host': proxy_host,
                    'port': proxy_port,
                    'username': proxy_username,
                    'password': proxy_password
                }
        
        # 指纹配置
        print("\n🔍 指纹配置:")
        use_custom_fingerprint = input("是否自定义指纹? (y/n): ").strip().lower() == 'y'
        fingerprint_params = {}
        
        if use_custom_fingerprint:
            resolution = input("分辨率 (如: 1920x1080): ").strip()
            if resolution:
                fingerprint_params['resolution'] = resolution
            
            timezone = input("时区 (如: Asia/Shanghai): ").strip()
            if timezone:
                fingerprint_params['timeZone'] = timezone
            
            language = input("语言 (如: en-US): ").strip()
            if language:
                fingerprint_params['languages'] = language
        
        # 创建配置
        config = {
            'username': username,
            'password': password,
            'proxy': proxy_config,
            'group': group,
            'url': url,
            'fingerprint': fingerprint_params
        }
        
        # 创建浏览器
        browser_id = self.manager.create_browser_profile(name, config)
        
        if browser_id:
            print(f"\n✅ 浏览器配置创建成功!")
            open_now = input("是否立即打开浏览器? (y/n): ").strip().lower() == 'y'
            if open_now:
                self.manager.open_browser(name)
        else:
            print("\n❌ 浏览器配置创建失败!")
    
    def open_browser_interactive(self):
        """交互式打开浏览器"""
        print("\n🚀 打开浏览器")
        print("-" * 40)
        
        self.manager.list_browsers()
        name = input("\n请输入要打开的浏览器名称: ").strip()
        
        if name:
            self.manager.open_browser(name)
        else:
            print("❌ 浏览器名称不能为空")
    
    def close_browser_interactive(self):
        """交互式关闭浏览器"""
        print("\n🔒 关闭浏览器")
        print("-" * 40)
        
        self.manager.list_browsers()
        name = input("\n请输入要关闭的浏览器名称 (输入 'all' 关闭所有): ").strip()
        
        if name == 'all':
            self.manager.close_all_browsers()
        elif name:
            self.manager.close_browser(name)
        else:
            print("❌ 浏览器名称不能为空")
    
    def delete_browser_interactive(self):
        """交互式删除浏览器"""
        print("\n🗑️ 删除浏览器")
        print("-" * 40)
        
        self.manager.list_browsers()
        name = input("\n请输入要删除的浏览器名称: ").strip()
        
        if name:
            confirm = input(f"确认删除浏览器 '{name}'? (y/n): ").strip().lower() == 'y'
            if confirm:
                self.manager.delete_browser(name)
            else:
                print("❌ 操作已取消")
        else:
            print("❌ 浏览器名称不能为空")
    
    def batch_operations(self):
        """批量操作菜单"""
        print("\n📦 批量操作")
        print("-" * 40)
        print("1. 批量创建浏览器")
        print("2. 批量打开浏览器")
        print("3. 批量关闭浏览器")
        print("4. 批量删除浏览器")
        print("0. 返回主菜单")
        
        choice = input("\n请选择操作: ").strip()
        
        if choice == '1':
            self.batch_create_browsers()
        elif choice == '2':
            self.batch_open_browsers()
        elif choice == '3':
            self.batch_close_browsers()
        elif choice == '4':
            self.batch_delete_browsers()
    
    def batch_create_browsers(self):
        """批量创建浏览器"""
        print("\n📝 批量创建浏览器")
        print("-" * 40)
        
        count = input("创建数量: ").strip()
        try:
            count = int(count)
        except:
            print("❌ 请输入有效数字")
            return
        
        prefix = input("名称前缀: ").strip() or "Browser"
        group = input("分组名称: ").strip() or "batch"
        
        print(f"\n🔧 开始创建 {count} 个浏览器...")
        
        for i in range(1, count + 1):
            name = f"{prefix}_{i:03d}"
            config = {
                'username': f"user_{i:03d}",
                'password': f"pass_{i:03d}",
                'group': group,
                'proxy': None,
                'url': '',
                'fingerprint': {}
            }
            
            browser_id = self.manager.create_browser_profile(name, config)
            if browser_id:
                print(f"✅ 创建成功: {name}")
            else:
                print(f"❌ 创建失败: {name}")
            
            time.sleep(0.5)  # 避免请求过快
        
        print(f"\n🎉 批量创建完成!")
    
    def batch_open_browsers(self):
        """批量打开浏览器"""
        print("\n🚀 批量打开浏览器")
        print("-" * 40)
        
        group = input("分组名称 (留空表示所有): ").strip()
        
        browsers_to_open = []
        for name, info in self.manager.configs.items():
            if not group or info['config'].get('group') == group:
                browsers_to_open.append(name)
        
        if not browsers_to_open:
            print("❌ 没有找到匹配的浏览器")
            return
        
        print(f"\n找到 {len(browsers_to_open)} 个浏览器:")
        for name in browsers_to_open:
            print(f"  - {name}")
        
        confirm = input("\n确认打开这些浏览器? (y/n): ").strip().lower() == 'y'
        if not confirm:
            print("❌ 操作已取消")
            return
        
        for name in browsers_to_open:
            self.manager.open_browser(name)
            time.sleep(1)  # 避免同时打开太多
    
    def batch_close_browsers(self):
        """批量关闭浏览器"""
        print("\n🔒 批量关闭浏览器")
        self.manager.close_all_browsers()
    
    def batch_delete_browsers(self):
        """批量删除浏览器"""
        print("\n🗑️ 批量删除浏览器")
        print("-" * 40)
        
        group = input("分组名称 (留空表示所有): ").strip()
        
        browsers_to_delete = []
        for name, info in self.manager.configs.items():
            if not group or info['config'].get('group') == group:
                browsers_to_delete.append(name)
        
        if not browsers_to_delete:
            print("❌ 没有找到匹配的浏览器")
            return
        
        print(f"\n⚠️ 将删除 {len(browsers_to_delete)} 个浏览器:")
        for name in browsers_to_delete:
            print(f"  - {name}")
        
        confirm = input("\n确认删除这些浏览器? (输入 'DELETE' 确认): ").strip()
        if confirm != 'DELETE':
            print("❌ 操作已取消")
            return
        
        for name in browsers_to_delete:
            self.manager.delete_browser(name)
            time.sleep(0.5)
    
    def window_management(self):
        """窗口管理"""
        print("\n📐 窗口管理")
        print("-" * 40)
        print("1. 宫格排列")
        print("2. 对角线排列")
        print("0. 返回主菜单")
        
        choice = input("\n请选择排列方式: ").strip()
        
        if choice == '1':
            columns = input("每行列数 (默认: 3): ").strip()
            try:
                columns = int(columns) if columns else 3
            except:
                columns = 3
            self.manager.arrange_windows('box', columns)
        elif choice == '2':
            self.manager.arrange_windows('diagonal')
    
    def proxy_management(self):
        """代理管理"""
        print("\n🌐 代理管理")
        print("-" * 40)
        
        self.manager.list_browsers()
        name = input("\n请输入要更新代理的浏览器名称: ").strip()
        
        if not name or name not in self.manager.configs:
            print("❌ 浏览器不存在")
            return
        
        print("\n当前代理配置:")
        current_proxy = self.manager.configs[name]['config'].get('proxy')
        if current_proxy:
            print(f"  类型: {current_proxy['type']}")
            print(f"  地址: {current_proxy['host']}:{current_proxy['port']}")
        else:
            print("  无代理")
        
        print("\n新代理配置:")
        proxy_type = input("代理类型 (http/https/socks5/noproxy): ").strip()
        
        if proxy_type == 'noproxy':
            proxy_config = {'type': 'noproxy', 'host': '', 'port': '', 'username': '', 'password': ''}
        else:
            proxy_host = input("代理主机: ").strip()
            proxy_port = input("代理端口: ").strip()
            proxy_username = input("代理用户名 (可选): ").strip()
            proxy_password = input("代理密码 (可选): ").strip()
            
            proxy_config = {
                'type': proxy_type,
                'host': proxy_host,
                'port': proxy_port,
                'username': proxy_username,
                'password': proxy_password
            }
        
        self.manager.update_browser_proxy(name, proxy_config)
    
    def create_examples(self):
        """创建示例浏览器"""
        print("\n🎯 快速创建示例")
        print("-" * 40)
        
        examples = [
            {
                'name': 'Google_Chrome',
                'config': {
                    'username': 'google_user',
                    'password': 'google_pass',
                    'group': 'search_engines',
                    'url': 'https://www.google.com',
                    'fingerprint': {'languages': 'en-US', 'resolution': '1920x1080'}
                }
            },
            {
                'name': 'Facebook_Social',
                'config': {
                    'username': 'fb_user',
                    'password': 'fb_pass',
                    'group': 'social_media',
                    'url': 'https://www.facebook.com',
                    'fingerprint': {'languages': 'en-US', 'timeZone': 'America/New_York'}
                }
            },
            {
                'name': 'Amazon_Shopping',
                'config': {
                    'username': 'amazon_user',
                    'password': 'amazon_pass',
                    'group': 'ecommerce',
                    'url': 'https://www.amazon.com',
                    'fingerprint': {'languages': 'en-US', 'resolution': '1366x768'}
                }
            }
        ]
        
        print("将创建以下示例浏览器:")
        for example in examples:
            print(f"  - {example['name']} ({example['config']['group']})")
        
        confirm = input("\n确认创建示例浏览器? (y/n): ").strip().lower() == 'y'
        if not confirm:
            print("❌ 操作已取消")
            return
        
        for example in examples:
            browser_id = self.manager.create_browser_profile(example['name'], example['config'])
            if browser_id:
                print(f"✅ 创建成功: {example['name']}")
            else:
                print(f"❌ 创建失败: {example['name']}")
            time.sleep(0.5)
        
        print("\n🎉 示例浏览器创建完成!")
    
    def run(self):
        """运行命令行界面"""
        self.show_banner()
        
        while self.running:
            try:
                self.show_menu()
                choice = input("请选择操作 (0-9): ").strip()
                
                if choice == '1':
                    self.create_browser_interactive()
                elif choice == '2':
                    self.open_browser_interactive()
                elif choice == '3':
                    self.close_browser_interactive()
                elif choice == '4':
                    self.delete_browser_interactive()
                elif choice == '5':
                    self.manager.list_browsers()
                elif choice == '6':
                    self.batch_operations()
                elif choice == '7':
                    self.window_management()
                elif choice == '8':
                    self.proxy_management()
                elif choice == '9':
                    self.create_examples()
                elif choice == '0':
                    print("\n👋 感谢使用指纹浏览器管理系统!")
                    self.manager.close_all_browsers()
                    self.running = False
                else:
                    print("❌ 无效选择，请重新输入")
                
                if self.running:
                    input("\n按回车键继续...")
                    
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断")
                self.manager.close_all_browsers()
                self.running = False
            except Exception as e:
                print(f"\n❌ 发生错误: {e}")
                input("按回车键继续...")

def main():
    """主函数"""
    cli = BrowserCLI()
    cli.run()

if __name__ == "__main__":
    main()
