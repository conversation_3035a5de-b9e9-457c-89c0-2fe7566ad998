#!/usr/bin/env python3
"""
比特浏览器API接口封装
基于GitHub上的开源代码进行优化和改进
"""

import os
import time
import random
import requests
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from faker import Faker
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

faker = Faker()

# WebGL厂商和渲染器列表
WEBGL_VENDORS = ['Google Inc.', 'Microsoft', 'Apple Inc.', 'ARM', 'Intel Inc.', 'Qualcomm']
WEBGL_RENDERS = [
    'ANGLE (Intel(R) HD Graphics 520 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA GeForce GTX 1050 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA GeForce GTX 1660 Ti Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0)',
    'Intel(R) HD Graphics 4600',
    'Apple GPU',
    'Mali-G72'
]

COLOR_DEPTHS = [16, 24, 32]
SYSTEMS = ['Win32', 'Linux i686', 'Linux armv7l', 'MacIntel']
RESOLUTIONS = ['1024x768', '1280x800', '1280x960', '1920x1080', '1440x900', '1280x1024']

class BitBrowserAPI:
    def __init__(self, website_name="default"):
        """
        初始化比特浏览器API
        
        Args:
            website_name: 网站名称，用于浏览器窗口标识
        """
        self.website = website_name
        self.bit_port = self._get_bit_port()
        
    def _get_bit_port(self):
        """获取比特浏览器的本地端口"""
        try:
            # Windows路径
            json_file = fr'C:\Users\<USER>\AppData\Roaming\bitbrowser\config.json'
            
            if not os.path.exists(json_file):
                # 尝试枚举用户目录
                users = os.listdir('C:/Users')
                for user in users:
                    test_path = fr'C:\Users\<USER>\AppData\Roaming\bitbrowser\config.json'
                    if os.path.exists(test_path):
                        json_file = test_path
                        break
                else:
                    logger.error(f'请先安装比特浏览器: {json_file}')
                    return None
            
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            bit_api = data['localServerAddress']
            port = bit_api.split(':')[-1]
            
            # 测试端口是否可用
            if self._test_port(port):
                logger.info(f"比特浏览器端口: {port}")
                return port
            else:
                logger.error('请检查比特浏览器是否已经启动')
                return None
                
        except Exception as e:
            logger.error(f"获取比特浏览器端口失败: {e}")
            return None
    
    def _test_port(self, port):
        """测试端口是否可用"""
        try:
            url = f'http://127.0.0.1:{port}'
            res = requests.get(url, timeout=5)
            return res.status_code == 200
        except:
            return False
    
    def _request(self, endpoint, payload):
        """发送API请求"""
        if not self.bit_port:
            logger.error("比特浏览器未启动或端口获取失败")
            return {}
            
        endpoint = endpoint[1:] if endpoint.startswith('/') else endpoint
        api = f'http://127.0.0.1:{self.bit_port}/{endpoint}'
        
        for attempt in range(3):
            try:
                res = requests.post(api, json=payload, timeout=15)
                data = res.json()
                
                if data.get('success'):
                    return data
                else:
                    logger.error(f'API请求失败 {endpoint}: {data}')
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f'API请求异常 {endpoint}: {e}')
                time.sleep(1)
                
        return {}
    
    def _generate_random_fingerprint(self):
        """生成随机指纹配置"""
        return {
            'coreVersion': '104',
            'ostype': 'PC',
            'os': random.choice(SYSTEMS),
            'version': str(random.randint(100, 107)),
            'userAgent': '',
            'timeZone': '',
            'timeZoneOffset': 0,
            'isIpCreateTimeZone': True,
            'webRTC': '0',
            'position': '1',
            'isIpCreatePosition': True,
            'lat': '',
            'lng': '',
            'precisionData': '',
            'isIpCreateLanguage': False,
            'languages': 'en-US',
            'isIpCreateDisplayLanguage': False,
            'displayLanguages': 'en-US',
            'resolutionType': '0',
            'resolution': random.choice(RESOLUTIONS),
            'fontType': '0',
            'font': '',
            'canvas': '0',
            'canvasValue': random.randint(10000, 1000000),
            'webGL': '0',
            'webGLValue': random.randint(10000, 1000000),
            'webGLMeta': '0',
            'webGLManufacturer': random.choice(WEBGL_VENDORS),
            'webGLRender': random.choice(WEBGL_RENDERS),
            'audioContext': '0',
            'audioContextValue': random.randint(1, 100),
            'mediaDevice': '0',
            'mediaDeviceValue': random.randint(1, 100),
            'speechVoices': '0',
            'speechVoicesValue': random.randint(1, 100),
            'hardwareConcurrency': str(random.choice([2, 4, 6, 8])),
            'deviceMemory': str(random.choice([4, 8, 16, 32])),
            'doNotTrack': '1',
            'portScanProtect': '',
            'portWhiteList': '',
            'colorDepth': str(random.choice(COLOR_DEPTHS)),
            'devicePixelRatio': str(round(random.uniform(1.0, 2.0), 1)),
            'openWidth': 1280,
            'openHeight': 1000,
            'ignoreHttpsErrors': True,
            'clientRectNoiseEnabled': True,
            'clientRectNoiseValue': random.randint(1, 999999),
            'deviceInfoEnabled': True,
            'computerName': f'Computer-{faker.first_name()}',
            'macAddr': '-'.join(['%02x' % faker.pyint(0, 255) for i in range(6)]).upper()
        }
    
    def create_browser(self, name, username='', password='', proxy_config=None, **kwargs):
        """
        创建浏览器配置
        
        Args:
            name: 浏览器名称
            username: 用户名
            password: 密码
            proxy_config: 代理配置 {'type': 'http', 'host': '', 'port': '', 'username': '', 'password': ''}
            **kwargs: 其他配置参数
        """
        logger.info(f'创建浏览器: {name}')
        
        # 基础配置
        payload = {
            "groupId": "",
            "platform": kwargs.get('platform', ''),
            "platformIcon": kwargs.get('platformIcon', 'other'),
            "url": kwargs.get('url', ''),
            "name": f'{self.website}:{name}',
            "remark": kwargs.get('remark', f'{username}----{password}'),
            "userName": username,
            "password": password,
            "cookie": kwargs.get('cookie', ''),
            "proxyMethod": 2,
            "proxyType": 'noproxy',
            "host": '',
            "port": '',
            "proxyUserName": '',
            "proxyPassword": '',
            'abortImage': kwargs.get('abortImage', False),
            'abortMedia': kwargs.get('abortMedia', False),
            'syncTabs': kwargs.get('syncTabs', False),
            'syncCookies': kwargs.get('syncCookies', True),
            'syncBookmarks': kwargs.get('syncBookmarks', True),
            'syncHistory': kwargs.get('syncHistory', True),
            'allowedSignin': kwargs.get('allowedSignin', True),
            'muteAudio': kwargs.get('muteAudio', True),
        }
        
        # 配置代理
        if proxy_config:
            payload.update({
                'proxyType': proxy_config.get('type', 'noproxy'),
                'host': proxy_config.get('host', ''),
                'port': proxy_config.get('port', ''),
                'proxyUserName': proxy_config.get('username', ''),
                'proxyPassword': proxy_config.get('password', ''),
            })
        
        # 生成指纹
        fingerprint = self._generate_random_fingerprint()
        
        # 从kwargs更新指纹配置
        for k, v in kwargs.items():
            if k in fingerprint:
                fingerprint[k] = v
        
        payload['browserFingerPrint'] = fingerprint
        
        # 检查是否已存在同名浏览器
        existing_browsers = self.get_browser_list(name=name)
        if existing_browsers:
            payload['id'] = existing_browsers[0]['id']
            # 保留原有指纹
            browser_detail = self.get_browser_detail(existing_browsers[0]['id'])
            if browser_detail:
                payload['browserFingerPrint'] = browser_detail['browserFingerPrint']
        
        data = self._request('browser/update', payload)
        
        if data.get('success'):
            browser_id = data['data']['id']
            logger.info(f'浏览器创建成功: {name} (ID: {browser_id})')
            return browser_id
        else:
            logger.error(f'浏览器创建失败: {name}')
            return None

    def open_browser(self, browser_id, load_extensions=True, args=None):
        """
        打开浏览器

        Args:
            browser_id: 浏览器ID
            load_extensions: 是否加载扩展
            args: 启动参数
        """
        logger.info(f'打开浏览器: {browser_id}')

        if args is None:
            args = []

        payload = {
            'id': browser_id,
            'loadExtensions': load_extensions,
            'args': args,
            'extractIp': True
        }

        data = self._request('browser/open', payload)

        if data.get('success'):
            try:
                options = Options()
                ws_url = data['data']['http']
                options.add_experimental_option('debuggerAddress', ws_url)
                options.add_argument("--no-default-browser-check")
                options.add_argument("--no-first-run")
                options.add_argument("--no-sandbox")
                options.add_argument("--test-type")

                driver = webdriver.Chrome(options=options)

                # 随机窗口位置
                x = random.randint(0, 500)
                y = random.randint(10, 200)
                driver.set_window_position(x, y)

                # 关闭多余标签页
                self._close_extra_tabs(driver)

                logger.info(f'浏览器打开成功: {browser_id}')
                return driver

            except Exception as e:
                logger.error(f'浏览器打开失败: {browser_id}, 错误: {e}')
                return None
        else:
            logger.error(f'浏览器打开失败: {browser_id}')
            return None

    def _close_extra_tabs(self, driver):
        """关闭多余的标签页，只保留一个"""
        try:
            while True:
                windows = driver.window_handles
                if len(windows) <= 1:
                    break

                for idx, window in enumerate(windows[:-1]):
                    if window != driver.current_window_handle:
                        driver.switch_to.window(window)
                        driver.close()

                driver.switch_to.window(windows[-1])
                break
        except Exception as e:
            logger.warning(f'关闭多余标签页失败: {e}')

    def close_browser(self, browser_id):
        """关闭浏览器"""
        logger.info(f'关闭浏览器: {browser_id}')

        payload = {'id': browser_id}
        data = self._request('browser/close', payload)

        if data.get('success'):
            logger.info(f'浏览器关闭成功: {browser_id}')
            return True
        else:
            logger.error(f'浏览器关闭失败: {browser_id}')
            return False

    def delete_browser(self, browser_id):
        """删除浏览器"""
        logger.info(f'删除浏览器: {browser_id}')

        payload = {'id': browser_id}
        data = self._request('browser/delete', payload)

        if data.get('success'):
            logger.info(f'浏览器删除成功: {browser_id}')
            return True
        else:
            logger.error(f'浏览器删除失败: {browser_id}')
            return False

    def get_browser_detail(self, browser_id):
        """获取浏览器详情"""
        payload = {'id': browser_id}
        data = self._request('browser/detail', payload)

        if data.get('success'):
            return data.get('data')
        else:
            logger.error(f'获取浏览器详情失败: {browser_id}')
            return None

    def get_browser_list(self, page=0, page_size=100, name='', group_id=''):
        """获取浏览器列表"""
        payload = {
            "page": page,
            "pageSize": page_size,
        }

        if name:
            payload['name'] = f'{self.website}:{name}'
        if group_id:
            payload['groupId'] = group_id

        data = self._request('browser/list', payload)
        return data.get('data', {'list': []}).get('list', [])

    def update_browser_proxy(self, browser_id, proxy_config):
        """
        更新浏览器代理

        Args:
            browser_id: 浏览器ID
            proxy_config: 代理配置字典
        """
        logger.info(f'更新浏览器代理: {browser_id}')

        payload = {
            "ids": [browser_id],
            "ipCheckService": "ip-api",
            "proxyMethod": 2,
            "proxyType": proxy_config.get('type', 'noproxy'),
            "host": proxy_config.get('host', ''),
            "port": int(proxy_config.get('port', 0)) if proxy_config.get('port') else "",
            "proxyUserName": proxy_config.get('username', ''),
            "proxyPassword": proxy_config.get('password', ''),
        }

        data = self._request('browser/proxy/update', payload)

        if data.get('success'):
            logger.info(f'代理更新成功: {browser_id}')
            return True
        else:
            logger.error(f'代理更新失败: {browser_id}')
            return False

    # 分组管理方法
    def create_group(self, group_name):
        """创建分组"""
        logger.info(f'创建分组: {group_name}')

        payload = {'groupName': group_name, 'sortNum': 1}
        data = self._request('group/add', payload)

        if data.get('success'):
            group_id = data['data']['id']
            logger.info(f'分组创建成功: {group_name} (ID: {group_id})')
            return group_id
        else:
            logger.error(f'分组创建失败: {group_name}')
            return None

    def get_group_list(self, page=0, page_size=100):
        """获取分组列表"""
        payload = {'page': page, 'pageSize': page_size}
        data = self._request('group/list', payload)

        if data.get('success'):
            return data['data']['list']
        else:
            logger.error('获取分组列表失败')
            return []

    def delete_group(self, group_id):
        """删除分组"""
        logger.info(f'删除分组: {group_id}')

        payload = {'id': group_id}
        data = self._request('group/delete', payload)

        if data.get('success'):
            logger.info(f'分组删除成功: {group_id}')
            return True
        else:
            logger.error(f'分组删除失败: {group_id}')
            return False

    def get_or_create_group(self, group_name):
        """获取或创建分组"""
        # 查找现有分组
        groups = self.get_group_list()
        for group in groups:
            if group['groupName'] == group_name:
                return group['id']

        # 创建新分组
        return self.create_group(group_name)

    def update_browser_group(self, browser_id, group_name):
        """更新浏览器分组"""
        logger.info(f'更新浏览器分组: {browser_id} -> {group_name}')

        group_id = self.get_or_create_group(group_name)
        if not group_id:
            return False

        # 获取浏览器详情
        browser_detail = self.get_browser_detail(browser_id)
        if not browser_detail:
            return False

        # 更新分组
        browser_detail['groupId'] = group_id
        data = self._request('browser/update', browser_detail)

        if data.get('success'):
            logger.info(f'浏览器分组更新成功: {browser_id} -> {group_name}')
            return True
        else:
            logger.error(f'浏览器分组更新失败: {browser_id} -> {group_name}')
            return False

    # 便捷方法
    def create_and_open_browser(self, name, username='', password='', proxy_config=None, **kwargs):
        """创建并打开浏览器的便捷方法"""
        browser_id = self.create_browser(name, username, password, proxy_config, **kwargs)
        if browser_id:
            driver = self.open_browser(browser_id)
            if driver:
                return driver, browser_id
            else:
                return None, browser_id
        else:
            return None, None

    def batch_close_browsers(self, browser_ids):
        """批量关闭浏览器"""
        results = []
        for browser_id in browser_ids:
            result = self.close_browser(browser_id)
            results.append(result)
        return results

    def batch_delete_browsers(self, browser_ids):
        """批量删除浏览器"""
        results = []
        for browser_id in browser_ids:
            result = self.delete_browser(browser_id)
            results.append(result)
        return results

    def arrange_windows(self, arrangement_type='box', start_x=0, start_y=0,
                       width=500, height=500, columns=3, space_x=0, space_y=0):
        """
        窗口排列

        Args:
            arrangement_type: 排列方式 'box'(宫格) 或 'diagonal'(对角线)
            start_x, start_y: 起始位置
            width, height: 窗口大小
            columns: 宫格排列时每行列数
            space_x, space_y: 间距
        """
        if arrangement_type not in ['box', 'diagonal']:
            arrangement_type = 'box'

        if width < 500:
            width = 500
        if height < 200:
            height = 200

        payload = {
            'type': arrangement_type,
            'startX': start_x,
            'startY': start_y,
            'width': width,
            'height': height,
            'col': columns,
            'spaceX': space_x,
            'spaceY': space_y,
            'offsetX': 0,
            'offsetY': 0
        }

        data = self._request('windowbounds', payload)

        if data.get('success'):
            logger.info('窗口排列成功')
            return True
        else:
            logger.error('窗口排列失败')
            return False
