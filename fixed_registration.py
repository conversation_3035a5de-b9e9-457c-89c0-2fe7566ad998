#!/usr/bin/env python3
"""
修复版本的注册脚本 - 直接指定Chrome路径
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Chrome路径
CHROME_PATH = r"E:\Google\Chrome\Application\chrome.exe"

def test_chrome_setup():
    """测试Chrome设置"""
    print("=== 测试Chrome设置 ===")
    
    # 检查Chrome是否存在
    if not os.path.exists(CHROME_PATH):
        print(f"❌ Chrome未找到: {CHROME_PATH}")
        return False
    
    print(f"✅ Chrome找到: {CHROME_PATH}")
    
    try:
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式测试
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.binary_location = CHROME_PATH
        
        # 获取ChromeDriver
        service = Service(ChromeDriverManager().install())
        
        # 创建WebDriver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 测试访问网页
        driver.get("https://www.google.com")
        title = driver.title
        print(f"✅ 测试成功！页面标题: {title}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

class SimpleRegistrationBot:
    def __init__(self, headless=False):
        """初始化简单注册机器人"""
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置Chrome WebDriver - 无痕模式"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")

            # 直接指定Chrome路径
            chrome_options.binary_location = CHROME_PATH

            # 启用无痕模式
            chrome_options.add_argument("--incognito")

            # 每次启动新的浏览器实例
            chrome_options.add_argument("--new-window")

            # 添加其他选项
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 禁用保存密码提示
            chrome_options.add_experimental_option("prefs", {
                "credentials_enable_service": False,
                "profile.password_manager_enabled": False
            })

            # 用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

            # 获取ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)

            logger.info("Chrome无痕模式 WebDriver 初始化成功")

        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def navigate_to_page(self, url):
        """导航到页面"""
        try:
            logger.info(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(2)
            return True
        except Exception as e:
            logger.error(f"无法访问页面: {e}")
            return False
    
    def fill_form_field(self, selectors, value):
        """填写表单字段"""
        for selector in selectors:
            try:
                if selector.startswith('#') or selector.startswith('.') or '[' in selector:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                else:
                    element = self.driver.find_element(By.NAME, selector)
                
                element.clear()
                for char in value:
                    element.send_keys(char)
                    time.sleep(random.uniform(0.05, 0.15))
                
                logger.info(f"字段填写成功: {selector}")
                return True
            except NoSuchElementException:
                continue
        
        logger.warning(f"未找到字段: {selectors}")
        return False
    
    def click_submit(self, selectors):
        """点击提交按钮"""
        for selector in selectors:
            try:
                if selector.startswith('#') or selector.startswith('.') or '[' in selector:
                    button = self.driver.find_element(By.CSS_SELECTOR, selector)
                else:
                    button = self.driver.find_element(By.NAME, selector)
                
                self.driver.execute_script("arguments[0].scrollIntoView();", button)
                time.sleep(1)
                button.click()
                logger.info("提交按钮点击成功")
                return True
            except NoSuchElementException:
                continue
        
        logger.warning(f"未找到提交按钮: {selectors}")
        return False
    
    def register(self, url, email, password, username=None):
        """执行注册"""
        try:
            # 1. 访问注册页面
            if not self.navigate_to_page(url):
                return False
            
            # 2. 填写邮箱
            email_selectors = ["input[type='email']", "input[name='email']", "#email"]
            if not self.fill_form_field(email_selectors, email):
                logger.error("邮箱填写失败")
                return False
            
            # 3. 填写密码
            password_selectors = ["input[type='password']", "input[name='password']", "#password"]
            if not self.fill_form_field(password_selectors, password):
                logger.error("密码填写失败")
                return False
            
            # 4. 填写用户名（如果需要）
            if username:
                username_selectors = ["input[name='username']", "#username"]
                self.fill_form_field(username_selectors, username)
            
            # 5. 点击提交
            submit_selectors = ["button[type='submit']", "input[type='submit']", ".submit-btn", ".register-btn"]
            if not self.click_submit(submit_selectors):
                logger.error("提交失败")
                return False
            
            # 6. 等待结果
            time.sleep(3)
            logger.info("注册流程完成")
            return True
            
        except Exception as e:
            logger.error(f"注册过程出错: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("=== 修复版注册脚本测试 ===\n")
    
    # 首先测试Chrome设置
    if not test_chrome_setup():
        print("Chrome设置测试失败，无法继续")
        return
    
    print("\n=== 开始访问订阅页面 ===")

    # 示例数据
    test_url = "https://app.augmentcode.com/account/subscription"  # 订阅页面URL
    test_email = "<EMAIL>"
    test_password = "SecurePassword123!"
    test_username = "testuser"
    
    # 创建机器人
    bot = SimpleRegistrationBot(headless=False)  # 设置为False以查看过程
    
    try:
        # 执行注册
        success = bot.register(test_url, test_email, test_password, test_username)
        
        if success:
            print("✅ 注册流程执行完成")
        else:
            print("❌ 注册流程失败")
            
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        
    finally:
        bot.close()

if __name__ == "__main__":
    main()
