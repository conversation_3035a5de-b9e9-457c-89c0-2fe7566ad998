#!/usr/bin/env python3
"""
指纹浏览器快速启动脚本
提供快速创建和使用浏览器的功能
"""

from fingerprint_browser_manager import FingerprintBrowserManager
import time

def quick_start_menu():
    """快速启动菜单"""
    print("🚀 指纹浏览器快速启动")
    print("=" * 50)
    print("1. 创建单个浏览器并打开")
    print("2. 创建多个浏览器并批量打开")
    print("3. 创建Augment Code专用浏览器")
    print("4. 创建代理浏览器")
    print("5. 创建测试浏览器组")
    print("0. 退出")
    print("-" * 50)

def create_single_browser():
    """创建单个浏览器"""
    print("\n🔧 创建单个浏览器")
    print("-" * 30)
    
    manager = FingerprintBrowserManager("QuickStart")
    
    # 获取用户输入
    name = input("浏览器名称 (默认: QuickBrowser): ").strip() or "QuickBrowser"
    url = input("启动URL (默认: https://www.google.com): ").strip() or "https://www.google.com"
    
    # 创建配置
    config = {
        'username': f'{name}_user',
        'password': f'{name}_pass',
        'group': 'quick_start',
        'url': url,
        'fingerprint': {
            'resolution': '1920x1080',
            'timeZone': 'Asia/Shanghai',
            'languages': 'zh-CN,zh'
        }
    }
    
    # 创建并打开浏览器
    print(f"\n🔧 创建浏览器: {name}")
    browser_id = manager.create_browser_profile(name, config)
    
    if browser_id:
        print("✅ 浏览器创建成功")
        
        open_now = input("是否立即打开? (y/n): ").strip().lower() == 'y'
        if open_now:
            print("🚀 打开浏览器...")
            driver = manager.open_browser(name)
            if driver:
                print("✅ 浏览器已打开")
                print(f"📄 页面标题: {driver.title}")
                input("按回车键关闭浏览器...")
                manager.close_browser(name)
            else:
                print("❌ 浏览器打开失败")
    else:
        print("❌ 浏览器创建失败")

def create_multiple_browsers():
    """创建多个浏览器"""
    print("\n📦 批量创建浏览器")
    print("-" * 30)
    
    manager = FingerprintBrowserManager("BatchQuickStart")
    
    # 获取用户输入
    try:
        count = int(input("创建数量 (默认: 3): ").strip() or "3")
    except:
        count = 3
    
    prefix = input("名称前缀 (默认: Browser): ").strip() or "Browser"
    url = input("启动URL (默认: https://www.google.com): ").strip() or "https://www.google.com"
    
    print(f"\n🔧 创建 {count} 个浏览器...")
    
    browser_names = []
    for i in range(1, count + 1):
        name = f"{prefix}_{i:02d}"
        config = {
            'username': f'user_{i:02d}',
            'password': f'pass_{i:02d}',
            'group': 'batch_quick',
            'url': url,
            'fingerprint': {
                'resolution': '1920x1080',
                'timeZone': 'Asia/Shanghai',
                'languages': 'zh-CN,zh'
            }
        }
        
        browser_id = manager.create_browser_profile(name, config)
        if browser_id:
            browser_names.append(name)
            print(f"✅ 创建成功: {name}")
        else:
            print(f"❌ 创建失败: {name}")
        
        time.sleep(0.3)
    
    if browser_names:
        print(f"\n✅ 成功创建 {len(browser_names)} 个浏览器")
        
        open_all = input("是否批量打开所有浏览器? (y/n): ").strip().lower() == 'y'
        if open_all:
            print("🚀 批量打开浏览器...")
            for name in browser_names:
                driver = manager.open_browser(name)
                if driver:
                    print(f"✅ 打开成功: {name}")
                else:
                    print(f"❌ 打开失败: {name}")
                time.sleep(1)
            
            # 排列窗口
            print("📐 排列窗口...")
            manager.arrange_windows('box', columns=3)
            
            input("按回车键关闭所有浏览器...")
            manager.close_all_browsers()

def create_augment_code_browser():
    """创建Augment Code专用浏览器"""
    print("\n🎯 创建Augment Code专用浏览器")
    print("-" * 40)
    
    manager = FingerprintBrowserManager("AugmentCode")
    
    # 使用预设的Augment Code配置
    config = {
        'username': '<EMAIL>',
        'password': 'Zui156997*',
        'group': 'augment_code',
        'url': 'https://app.augmentcode.com/account/subscription',
        'fingerprint': {
            'resolution': '1920x1080',
            'timeZone': 'America/New_York',
            'languages': 'en-US',
            'webGL': '0',
            'canvas': '0',
            'hardwareConcurrency': '8',
            'deviceMemory': '16'
        }
    }
    
    name = "AugmentCodeBrowser"
    
    print(f"🔧 创建Augment Code浏览器...")
    browser_id = manager.create_browser_profile(name, config)
    
    if browser_id:
        print("✅ Augment Code浏览器创建成功")
        
        open_now = input("是否立即打开进行注册? (y/n): ").strip().lower() == 'y'
        if open_now:
            print("🚀 打开Augment Code浏览器...")
            driver = manager.open_browser(name)
            if driver:
                print("✅ 浏览器已打开")
                print("📄 已导航到Augment Code订阅页面")
                print("💡 您现在可以手动完成注册流程")
                print("💡 或者集成自动化脚本进行半自动注册")
                
                input("按回车键关闭浏览器...")
                manager.close_browser(name)
            else:
                print("❌ 浏览器打开失败")
    else:
        print("❌ Augment Code浏览器创建失败")

def create_proxy_browser():
    """创建代理浏览器"""
    print("\n🌐 创建代理浏览器")
    print("-" * 30)
    
    manager = FingerprintBrowserManager("ProxyTest")
    
    # 获取代理信息
    print("请输入代理信息:")
    proxy_type = input("代理类型 (http/socks5): ").strip() or "http"
    proxy_host = input("代理主机: ").strip()
    proxy_port = input("代理端口: ").strip()
    proxy_user = input("代理用户名 (可选): ").strip()
    proxy_pass = input("代理密码 (可选): ").strip()
    
    if not proxy_host or not proxy_port:
        print("❌ 代理主机和端口不能为空")
        return
    
    # 创建代理配置
    proxy_config = {
        'type': proxy_type,
        'host': proxy_host,
        'port': proxy_port,
        'username': proxy_user,
        'password': proxy_pass
    }
    
    config = {
        'username': 'proxy_user',
        'password': 'proxy_pass',
        'group': 'proxy_test',
        'url': 'https://httpbin.org/ip',  # 用于检查IP
        'proxy': proxy_config,
        'fingerprint': {
            'resolution': '1366x768',
            'timeZone': 'UTC',
            'languages': 'en-US'
        }
    }
    
    name = f"ProxyBrowser_{proxy_host}_{proxy_port}"
    
    print(f"\n🔧 创建代理浏览器...")
    browser_id = manager.create_browser_profile(name, config)
    
    if browser_id:
        print("✅ 代理浏览器创建成功")
        
        test_proxy = input("是否立即测试代理? (y/n): ").strip().lower() == 'y'
        if test_proxy:
            print("🚀 打开浏览器测试代理...")
            driver = manager.open_browser(name)
            if driver:
                print("✅ 浏览器已打开")
                print("📄 正在检查IP地址...")
                print("💡 请查看页面显示的IP是否为代理IP")
                
                input("按回车键关闭浏览器...")
                manager.close_browser(name)
            else:
                print("❌ 浏览器打开失败")
    else:
        print("❌ 代理浏览器创建失败")

def create_test_browser_group():
    """创建测试浏览器组"""
    print("\n🧪 创建测试浏览器组")
    print("-" * 30)
    
    manager = FingerprintBrowserManager("TestGroup")
    
    # 预定义的测试配置
    test_configs = [
        {
            'name': 'Chrome_Desktop',
            'config': {
                'username': 'chrome_user',
                'password': 'chrome_pass',
                'group': 'test_browsers',
                'url': 'https://www.whatismybrowser.com',
                'fingerprint': {
                    'resolution': '1920x1080',
                    'timeZone': 'America/New_York',
                    'languages': 'en-US',
                    'hardwareConcurrency': '8',
                    'deviceMemory': '16'
                }
            }
        },
        {
            'name': 'Firefox_Mobile',
            'config': {
                'username': 'firefox_user',
                'password': 'firefox_pass',
                'group': 'test_browsers',
                'url': 'https://www.whatismybrowser.com',
                'fingerprint': {
                    'resolution': '375x667',
                    'timeZone': 'Asia/Tokyo',
                    'languages': 'ja-JP,ja',
                    'hardwareConcurrency': '4',
                    'deviceMemory': '4'
                }
            }
        },
        {
            'name': 'Safari_Mac',
            'config': {
                'username': 'safari_user',
                'password': 'safari_pass',
                'group': 'test_browsers',
                'url': 'https://www.whatismybrowser.com',
                'fingerprint': {
                    'resolution': '1440x900',
                    'timeZone': 'America/Los_Angeles',
                    'languages': 'en-US',
                    'hardwareConcurrency': '6',
                    'deviceMemory': '8'
                }
            }
        }
    ]
    
    print("🔧 创建测试浏览器组...")
    
    browser_names = []
    for test_config in test_configs:
        name = test_config['name']
        config = test_config['config']
        
        browser_id = manager.create_browser_profile(name, config)
        if browser_id:
            browser_names.append(name)
            print(f"✅ 创建成功: {name}")
        else:
            print(f"❌ 创建失败: {name}")
        
        time.sleep(0.5)
    
    if browser_names:
        print(f"\n✅ 成功创建 {len(browser_names)} 个测试浏览器")
        
        open_all = input("是否打开所有测试浏览器? (y/n): ").strip().lower() == 'y'
        if open_all:
            print("🚀 打开所有测试浏览器...")
            for name in browser_names:
                driver = manager.open_browser(name)
                if driver:
                    print(f"✅ 打开成功: {name}")
                else:
                    print(f"❌ 打开失败: {name}")
                time.sleep(1)
            
            # 排列窗口
            print("📐 排列窗口...")
            manager.arrange_windows('box', columns=2)
            
            print("💡 您现在可以比较不同浏览器的指纹信息")
            input("按回车键关闭所有浏览器...")
            manager.close_all_browsers()

def main():
    """主函数"""
    while True:
        try:
            quick_start_menu()
            choice = input("请选择操作 (0-5): ").strip()
            
            if choice == '1':
                create_single_browser()
            elif choice == '2':
                create_multiple_browsers()
            elif choice == '3':
                create_augment_code_browser()
            elif choice == '4':
                create_proxy_browser()
            elif choice == '5':
                create_test_browser_group()
            elif choice == '0':
                print("\n👋 感谢使用指纹浏览器快速启动!")
                break
            else:
                print("❌ 无效选择，请重新输入")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
