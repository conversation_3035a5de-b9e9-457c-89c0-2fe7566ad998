#!/usr/bin/env python3
"""
用户配置文件
请修改以下信息为您的实际注册信息
"""

# 用户注册信息配置
USER_INFO = {
    # 必填信息
    "email": "<EMAIL>",  # 请修改为您的邮箱地址
    "password": "Zui156997*",  # 请修改为您的密码
    
    # 可选信息
    "username": "your_username",  # 用户名（如果网站需要）
    "first_name": "Your",  # 名字
    "last_name": "Name",  # 姓氏
    "confirm_password": None,  # 确认密码（如果为None，将使用相同的密码）
    
    # 其他可能需要的信息
    "phone": "",  # 电话号码
    "company": "",  # 公司名称
    "job_title": "",  # 职位
}

# 网站URL配置
WEBSITE_URLS = {
    "home": "https://app.augmentcode.com",
    "register": "https://app.augmentcode.com/account/subscription",  # 实际的注册页面
    "login": "https://app.augmentcode.com/login",
    "subscription": "https://app.augmentcode.com/account/subscription",
    "dashboard": "https://app.augmentcode.com/dashboard"
}

# 浏览器配置
BROWSER_CONFIG = {
    "chrome_path": r"E:\Google\Chrome\Application\chrome.exe",
    "headless": False,  # 是否使用无头模式
    "incognito": True,  # 是否使用无痕模式
    "wait_timeout": 15,  # 等待超时时间（秒）
    "typing_delay": (0.05, 0.15),  # 打字延迟范围（秒）
}

# 表单选择器配置（高级用户可以修改）
FORM_SELECTORS = {
    "email": [
        "input[type='email']",
        "input[name='email']",
        "input[id='email']",
        "input[placeholder*='email' i]",
        "input[name='username']",  # 有些网站用username作为邮箱
    ],
    
    "password": [
        "input[type='password'][name*='password']:not([name*='confirm'])",
        "input[type='password'][id*='password']:not([id*='confirm'])",
        "input[type='password']:first-of-type",
        "input[name='password']",
        "input[id='password']"
    ],
    
    "confirm_password": [
        "input[name*='confirm']",
        "input[id*='confirm']",
        "input[placeholder*='confirm' i]",
        "input[type='password']:last-of-type",
        "input[name='confirmPassword']",
        "input[name='confirm_password']",
        "input[id='confirmPassword']",
        "input[id='confirm_password']"
    ],
    
    "username": [
        "input[name='username']",
        "input[id='username']",
        "input[placeholder*='username' i]",
        "input[name='name']",
        "input[id='name']"
    ],
    
    "first_name": [
        "input[name='firstName']",
        "input[name='first_name']",
        "input[id='firstName']",
        "input[id='first_name']",
        "input[placeholder*='first' i]"
    ],
    
    "last_name": [
        "input[name='lastName']",
        "input[name='last_name']",
        "input[id='lastName']",
        "input[id='last_name']",
        "input[placeholder*='last' i]"
    ],
    
    "phone": [
        "input[type='tel']",
        "input[name='phone']",
        "input[id='phone']",
        "input[placeholder*='phone' i]"
    ],
    
    "company": [
        "input[name='company']",
        "input[id='company']",
        "input[placeholder*='company' i]"
    ]
}

# 按钮选择器配置
BUTTON_SELECTORS = {
    "register": [
        "button[type='submit']",
        "input[type='submit']",
        "//button[contains(text(), 'Register')]",
        "//button[contains(text(), 'Sign up')]",
        "//button[contains(text(), 'Create Account')]",
        "//button[contains(text(), '注册')]",
        "//input[@value='Register']",
        "//input[@value='Sign up']",
        ".btn-primary",
        ".register-btn",
        ".signup-btn"
    ],
    
    "login": [
        "button[type='submit']",
        "input[type='submit']",
        "//button[contains(text(), 'Sign in')]",
        "//button[contains(text(), 'Login')]",
        "//button[contains(text(), 'Log in')]",
        "//button[contains(text(), '登录')]",
        "//input[@value='Sign in']",
        "//input[@value='Login']",
        ".btn-primary",
        ".login-btn",
        ".signin-btn"
    ]
}

# 链接选择器配置
LINK_SELECTORS = {
    "register": [
        "a[href*='register']",
        "a[href*='signup']",
        "a[href*='sign-up']",
        "//a[contains(text(), 'Register')]",
        "//a[contains(text(), 'Sign up')]",
        "//a[contains(text(), 'Create Account')]",
        "//a[contains(text(), '注册')]"
    ],
    
    "login": [
        "a[href*='login']",
        "a[href*='signin']",
        "a[href*='sign-in']",
        "//a[contains(text(), 'Sign in')]",
        "//a[contains(text(), 'Login')]",
        "//a[contains(text(), 'Log in')]",
        "//a[contains(text(), '登录')]"
    ]
}

# 成功指示器配置
SUCCESS_INDICATORS = {
    "register": [
        "//div[contains(text(), 'success')]",
        "//div[contains(text(), 'welcome')]",
        "//div[contains(text(), 'verify')]",
        "//div[contains(text(), 'email')]",
        ".success-message",
        ".welcome-message",
        ".verification-message"
    ],
    
    "login": [
        "//div[contains(text(), 'dashboard')]",
        "//div[contains(text(), 'welcome')]",
        ".dashboard",
        ".user-menu",
        ".profile-menu"
    ]
}

def validate_config():
    """验证配置是否正确"""
    errors = []
    
    # 检查必填字段
    if USER_INFO["email"] == "<EMAIL>":
        errors.append("请修改邮箱地址")
    
    if USER_INFO["password"] == "YourSecurePassword123!":
        errors.append("请修改密码")
    
    if not USER_INFO["email"] or "@" not in USER_INFO["email"]:
        errors.append("邮箱格式不正确")
    
    if not USER_INFO["password"] or len(USER_INFO["password"]) < 6:
        errors.append("密码长度至少6位")
    
    # 检查Chrome路径
    import os
    if not os.path.exists(BROWSER_CONFIG["chrome_path"]):
        errors.append(f"Chrome路径不存在: {BROWSER_CONFIG['chrome_path']}")
    
    return errors

def print_config_status():
    """打印配置状态"""
    print("=== 当前配置状态 ===")
    print(f"邮箱: {USER_INFO['email']}")
    print(f"用户名: {USER_INFO['username']}")
    print(f"Chrome路径: {BROWSER_CONFIG['chrome_path']}")
    print(f"无痕模式: {BROWSER_CONFIG['incognito']}")
    print(f"无头模式: {BROWSER_CONFIG['headless']}")
    
    errors = validate_config()
    if errors:
        print("\n⚠️  配置错误:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("\n✅ 配置验证通过")
        return True

if __name__ == "__main__":
    print_config_status()
