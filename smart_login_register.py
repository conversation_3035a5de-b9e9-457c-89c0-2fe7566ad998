#!/usr/bin/env python3
"""
智能登录/注册脚本
处理分步骤的登录和注册流程
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 导入配置
from user_config import (
    USER_INFO, WEBSITE_URLS, BROWSER_CONFIG, 
    validate_config, print_config_status
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SmartLoginRegisterBot:
    def __init__(self, headless=None):
        """初始化智能登录注册机器人"""
        self.driver = None
        self.wait = None
        
        if headless is None:
            headless = BROWSER_CONFIG["headless"]
            
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")
            
            chrome_options.binary_location = BROWSER_CONFIG["chrome_path"]
            
            if BROWSER_CONFIG["incognito"]:
                chrome_options.add_argument("--incognito")
                chrome_options.add_argument("--new-window")
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            chrome_options.add_experimental_option("prefs", {
                "credentials_enable_service": False,
                "profile.password_manager_enabled": False,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0
            })
            
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, BROWSER_CONFIG["wait_timeout"])
            
            self.driver.maximize_window()
            
            logger.info("Chrome WebDriver 初始化成功")
            
        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def navigate_to_page(self, url):
        """导航到指定页面"""
        try:
            logger.info(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(3)
            
            page_title = self.driver.title
            current_url = self.driver.current_url
            logger.info(f"页面标题: {page_title}")
            logger.info(f"当前URL: {current_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"无法访问页面: {e}")
            return False
    
    def type_slowly(self, element, text):
        """模拟人类打字速度"""
        element.clear()
        delay_range = BROWSER_CONFIG["typing_delay"]
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(*delay_range))
    
    def find_and_fill_username(self, email):
        """查找并填写用户名/邮箱字段"""
        selectors = [
            "input[name='username']",
            "input[id='username']",
            "input[type='email']",
            "input[name='email']",
            "input[id='email']",
            "input[placeholder*='email' i]",
            "input[placeholder*='username' i]"
        ]
        
        for selector in selectors:
            try:
                field = self.driver.find_element(By.CSS_SELECTOR, selector)
                if field.is_displayed():
                    self.type_slowly(field, email)
                    logger.info(f"用户名/邮箱填写成功: {selector}")
                    return True
            except:
                continue
        
        logger.warning("未找到用户名/邮箱输入框")
        return False
    
    def find_and_fill_password(self, password, wait_time=10):
        """查找并填写密码字段，带等待机制"""
        selectors = [
            "input[type='password']",
            "input[name='password']",
            "input[id='password']"
        ]

        # 首先等待密码字段出现
        logger.info(f"等待密码字段出现，最多等待 {wait_time} 秒...")

        for i in range(wait_time):
            for selector in selectors:
                try:
                    field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if field.is_displayed():
                        self.type_slowly(field, password)
                        logger.info(f"密码填写成功: {selector}")
                        return True
                except:
                    continue

            # 如果没找到，等待1秒后重试
            time.sleep(1)
            logger.info(f"等待中... ({i+1}/{wait_time})")

        logger.warning("未找到密码输入框")
        return False
    
    def handle_human_verification(self):
        """处理人机验证"""
        try:
            logger.info("检查是否需要人机验证...")

            # 等待验证过程完成
            verification_selectors = [
                "//div[contains(text(), 'Verifying')]",
                "//span[contains(text(), 'Verifying')]",
                ".verifying",
                "[data-testid*='verify']"
            ]

            # 检查是否正在验证
            for selector in verification_selectors:
                try:
                    if selector.startswith("//"):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if element.is_displayed():
                        logger.info("检测到验证过程，等待完成...")
                        # 等待验证完成
                        for i in range(10):
                            time.sleep(1)
                            if not element.is_displayed():
                                logger.info("验证过程完成")
                                break
                            logger.info(f"等待验证完成... ({i+1}/10)")
                        break
                except:
                    continue

            # 查找人机验证复选框
            human_verify_selectors = [
                "//input[@type='checkbox']",
                "//div[contains(text(), 'Verify you are human')]//input",
                "//label[contains(text(), 'Verify you are human')]//input",
                "//span[contains(text(), 'Verify you are human')]//input",
                "[data-testid*='human']",
                ".human-verification input",
                ".captcha input"
            ]

            for selector in human_verify_selectors:
                try:
                    if selector.startswith("//"):
                        checkbox = self.driver.find_element(By.XPATH, selector)
                    else:
                        checkbox = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if checkbox.is_displayed() and not checkbox.is_selected():
                        self.driver.execute_script("arguments[0].scrollIntoView();", checkbox)
                        time.sleep(1)
                        checkbox.click()
                        logger.info(f"人机验证复选框点击成功: {selector}")
                        time.sleep(2)  # 等待验证完成
                        return True
                except:
                    continue

            # 查找"Verify you are human"文本附近的可点击元素
            try:
                verify_text_xpath = "//div[contains(text(), 'Verify you are human')] | //span[contains(text(), 'Verify you are human')] | //label[contains(text(), 'Verify you are human')]"
                verify_element = self.driver.find_element(By.XPATH, verify_text_xpath)
                if verify_element.is_displayed():
                    self.driver.execute_script("arguments[0].scrollIntoView();", verify_element)
                    time.sleep(1)
                    verify_element.click()
                    logger.info("人机验证区域点击成功")
                    time.sleep(2)
                    return True
            except:
                pass

            logger.info("未找到需要处理的人机验证")
            return True  # 如果没有验证，返回True继续流程

        except Exception as e:
            logger.error(f"处理人机验证失败: {e}")
            return False

    def click_continue_button(self):
        """点击Continue按钮"""
        button_texts = ["Continue", "Next", "Submit", "Sign in", "Login", "继续", "下一步", "提交", "登录"]

        for text in button_texts:
            try:
                # 尝试精确匹配
                xpath = f"//button[text()='{text}']"
                button = self.driver.find_element(By.XPATH, xpath)
                if button.is_displayed() and button.is_enabled():
                    self.driver.execute_script("arguments[0].scrollIntoView();", button)
                    time.sleep(1)
                    button.click()
                    logger.info(f"按钮点击成功: {text}")
                    return True
            except:
                try:
                    # 尝试包含匹配
                    xpath = f"//button[contains(text(), '{text}')]"
                    button = self.driver.find_element(By.XPATH, xpath)
                    if button.is_displayed() and button.is_enabled():
                        self.driver.execute_script("arguments[0].scrollIntoView();", button)
                        time.sleep(1)
                        button.click()
                        logger.info(f"按钮点击成功: {text}")
                        return True
                except:
                    continue

        # 尝试通过type=submit查找
        try:
            button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            if button.is_displayed() and button.is_enabled():
                self.driver.execute_script("arguments[0].scrollIntoView();", button)
                time.sleep(1)
                button.click()
                logger.info("Submit按钮点击成功")
                return True
        except:
            pass

        logger.warning("未找到可点击的Continue按钮")
        return False
    
    def analyze_current_page(self):
        """分析当前页面内容"""
        try:
            logger.info("=== 页面内容分析 ===")

            # 查找所有输入框
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            logger.info(f"找到 {len(inputs)} 个输入框:")

            for i, input_elem in enumerate(inputs):
                if input_elem.is_displayed():
                    input_type = input_elem.get_attribute("type")
                    input_name = input_elem.get_attribute("name")
                    input_id = input_elem.get_attribute("id")
                    input_placeholder = input_elem.get_attribute("placeholder")
                    logger.info(f"  输入框 {i+1}: type={input_type}, name={input_name}, id={input_id}, placeholder={input_placeholder}")

            # 查找所有按钮
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            logger.info(f"找到 {len(buttons)} 个按钮:")

            for i, button in enumerate(buttons):
                if button.is_displayed():
                    button_text = button.text
                    button_type = button.get_attribute("type")
                    logger.info(f"  按钮 {i+1}: text='{button_text}', type={button_type}")

            logger.info("=== 分析完成 ===")

        except Exception as e:
            logger.error(f"页面分析失败: {e}")

    def look_for_signup_option(self):
        """查找注册选项"""
        signup_texts = [
            "Sign up", "Register", "Create account", "Don't have an account",
            "注册", "创建账户", "没有账户"
        ]

        for text in signup_texts:
            try:
                # 查找链接
                xpath = f"//a[contains(text(), '{text}')]"
                link = self.driver.find_element(By.XPATH, xpath)
                if link.is_displayed():
                    link.click()
                    logger.info(f"注册链接点击成功: {text}")
                    time.sleep(3)
                    return True
            except:
                try:
                    # 查找按钮
                    xpath = f"//button[contains(text(), '{text}')]"
                    button = self.driver.find_element(By.XPATH, xpath)
                    if button.is_displayed():
                        button.click()
                        logger.info(f"注册按钮点击成功: {text}")
                        time.sleep(3)
                        return True
                except:
                    continue

        logger.info("未找到明显的注册选项")
        return False
    
    def handle_multi_step_flow(self):
        """处理多步骤登录/注册流程"""
        try:
            logger.info("开始处理多步骤流程...")
            
            # 步骤1: 访问订阅页面
            if not self.navigate_to_page(WEBSITE_URLS["subscription"]):
                return False
            
            # 步骤2: 填写用户名/邮箱
            if not self.find_and_fill_username(USER_INFO["email"]):
                logger.error("无法填写用户名/邮箱")
                return False

            # 步骤3: 处理人机验证
            if not self.handle_human_verification():
                logger.error("人机验证处理失败")
                return False

            # 步骤4: 点击Continue按钮
            if not self.click_continue_button():
                logger.error("无法点击Continue按钮")
                return False
            
            # 步骤4: 等待页面加载并分析页面内容
            logger.info("等待页面加载...")
            time.sleep(3)

            # 分析页面内容
            self.analyze_current_page()

            # 步骤5: 检查当前页面状态
            current_url = self.driver.current_url.lower()
            page_title = self.driver.title.lower()

            logger.info(f"第二步页面 - URL: {current_url}")
            logger.info(f"第二步页面 - 标题: {page_title}")

            # 步骤6: 根据页面状态决定下一步操作
            if "password" in page_title or "sign" in page_title:
                logger.info("检测到登录页面，尝试填写密码...")

                # 填写密码（带等待机制）
                if self.find_and_fill_password(USER_INFO["password"], wait_time=15):
                    # 点击登录按钮
                    if self.click_continue_button():
                        logger.info("登录流程完成")
                        time.sleep(5)
                        return True
                    else:
                        logger.error("无法点击登录按钮")
                        return False
                else:
                    logger.error("无法填写密码，可能需要注册新账户")
                    # 尝试查找注册选项
                    if self.look_for_signup_option():
                        logger.info("找到注册选项，重新开始流程...")
                        time.sleep(3)
                        return self.handle_multi_step_flow()
                    else:
                        return False
            
            elif "register" in current_url or "signup" in current_url:
                logger.info("检测到注册页面，尝试完成注册...")
                
                # 填写密码
                if self.find_and_fill_password(USER_INFO["password"]):
                    # 查找确认密码字段
                    confirm_password = USER_INFO["confirm_password"] or USER_INFO["password"]
                    confirm_selectors = [
                        "input[name*='confirm']",
                        "input[id*='confirm']",
                        "input[placeholder*='confirm' i]"
                    ]
                    
                    for selector in confirm_selectors:
                        try:
                            field = self.driver.find_element(By.CSS_SELECTOR, selector)
                            if field.is_displayed():
                                self.type_slowly(field, confirm_password)
                                logger.info(f"确认密码填写成功: {selector}")
                                break
                        except:
                            continue
                    
                    # 点击注册按钮
                    register_texts = ["Sign up", "Register", "Create account", "注册", "创建账户"]
                    for text in register_texts:
                        try:
                            xpath = f"//button[contains(text(), '{text}')]"
                            button = self.driver.find_element(By.XPATH, xpath)
                            if button.is_displayed():
                                button.click()
                                logger.info(f"注册按钮点击成功: {text}")
                                time.sleep(5)
                                return True
                        except:
                            continue
                    
                    logger.error("无法找到注册按钮")
                    return False
                else:
                    logger.error("无法填写密码")
                    return False
            
            else:
                logger.info("尝试查找注册选项...")
                if self.look_for_signup_option():
                    # 递归调用处理注册流程
                    time.sleep(3)
                    return self.handle_multi_step_flow()
                else:
                    logger.warning("无法确定下一步操作")
                    return False
            
        except Exception as e:
            logger.error(f"处理多步骤流程失败: {e}")
            return False
    
    def take_screenshot(self, filename):
        """截取屏幕截图"""
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"截图已保存: {filename}")
            return True
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False
    
    def wait_for_user(self, seconds=30):
        """等待用户操作"""
        logger.info(f"等待用户操作 {seconds} 秒...")
        time.sleep(seconds)
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("=== 智能登录/注册脚本 ===\n")
    
    # 检查配置
    if not print_config_status():
        print("\n请先修改 user_config.py 文件中的配置信息！")
        return
    
    print(f"\n目标页面: {WEBSITE_URLS['subscription']}")
    print("这个脚本会智能处理多步骤的登录/注册流程")
    
    confirm = input("\n是否继续？(y/n): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    bot = SmartLoginRegisterBot()
    
    try:
        print("\n开始智能登录/注册流程...")
        success = bot.handle_multi_step_flow()
        
        if success:
            print("✅ 登录/注册流程完成")
            bot.take_screenshot("smart_flow_result.png")
            
            # 尝试访问订阅页面
            print("正在访问订阅页面...")
            bot.navigate_to_page(WEBSITE_URLS["subscription"])
            bot.take_screenshot("final_subscription_page.png")
            
        else:
            print("❌ 登录/注册失败")
            bot.take_screenshot("smart_flow_error.png")
        
        # 等待用户查看结果
        bot.wait_for_user(60)
        
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        bot.take_screenshot("smart_flow_error.png")
        
    finally:
        input("\n按回车键关闭浏览器...")
        bot.close()

if __name__ == "__main__":
    main()
