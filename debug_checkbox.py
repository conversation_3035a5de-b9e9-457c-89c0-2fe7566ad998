#!/usr/bin/env python3
"""
调试复选框脚本 - 专门分析和处理人机验证复选框
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 导入配置
from user_config import (
    USER_INFO, WEBSITE_URLS, BROWSER_CONFIG, 
    validate_config, print_config_status
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CheckboxDebugBot:
    def __init__(self):
        """初始化调试机器人"""
        self.driver = None
        self.wait = None
        self.setup_driver()
    
    def setup_driver(self):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.binary_location = BROWSER_CONFIG["chrome_path"]
            chrome_options.add_argument("--incognito")
            chrome_options.add_argument("--new-window")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            self.driver.maximize_window()
            
            logger.info("Chrome WebDriver 初始化成功")
            
        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def navigate_and_fill_email(self):
        """访问页面并填写邮箱"""
        try:
            logger.info("访问页面并填写邮箱...")
            self.driver.get(WEBSITE_URLS['subscription'])
            time.sleep(3)
            
            # 填写邮箱
            email_field = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "input[name='username']")))
            email_field.clear()
            for char in USER_INFO["email"]:
                email_field.send_keys(char)
                time.sleep(0.1)
            
            logger.info("邮箱填写完成")
            time.sleep(2)
            return True
            
        except Exception as e:
            logger.error(f"访问页面或填写邮箱失败: {e}")
            return False
    
    def analyze_page_structure(self):
        """详细分析页面结构"""
        try:
            logger.info("=== 开始详细页面结构分析 ===")
            
            # 分析所有元素
            all_elements = self.driver.find_elements(By.XPATH, "//*")
            logger.info(f"页面总元素数量: {len(all_elements)}")
            
            # 查找所有输入元素
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            logger.info(f"\n找到 {len(inputs)} 个input元素:")
            for i, inp in enumerate(inputs):
                if inp.is_displayed():
                    input_type = inp.get_attribute("type")
                    input_name = inp.get_attribute("name")
                    input_id = inp.get_attribute("id")
                    input_class = inp.get_attribute("class")
                    input_value = inp.get_attribute("value")
                    is_selected = inp.is_selected() if input_type == "checkbox" else "N/A"
                    logger.info(f"  Input {i+1}: type={input_type}, name={input_name}, id={input_id}, class={input_class}, value={input_value}, selected={is_selected}")
            
            # 查找包含"Verify"文本的元素
            verify_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Verify') or contains(text(), 'verify') or contains(text(), 'human')]")
            logger.info(f"\n找到 {len(verify_elements)} 个包含验证文本的元素:")
            for i, elem in enumerate(verify_elements):
                if elem.is_displayed():
                    tag_name = elem.tag_name
                    text = elem.text
                    elem_class = elem.get_attribute("class")
                    elem_id = elem.get_attribute("id")
                    logger.info(f"  Verify元素 {i+1}: tag={tag_name}, text='{text}', class={elem_class}, id={elem_id}")
            
            # 查找所有div元素
            divs = self.driver.find_elements(By.TAG_NAME, "div")
            verify_divs = [div for div in divs if div.is_displayed() and ("verify" in div.text.lower() or "human" in div.text.lower())]
            logger.info(f"\n找到 {len(verify_divs)} 个包含验证文本的div:")
            for i, div in enumerate(verify_divs):
                div_class = div.get_attribute("class")
                div_id = div.get_attribute("id")
                div_text = div.text
                logger.info(f"  Verify Div {i+1}: class={div_class}, id={div_id}, text='{div_text}'")
            
            # 获取页面HTML源码中的相关部分
            page_source = self.driver.page_source
            if "Verify you are human" in page_source:
                logger.info("\n✅ 页面源码中找到 'Verify you are human' 文本")
                # 查找相关HTML片段
                start_idx = page_source.find("Verify you are human") - 200
                end_idx = page_source.find("Verify you are human") + 200
                if start_idx < 0:
                    start_idx = 0
                html_snippet = page_source[start_idx:end_idx]
                logger.info(f"相关HTML片段:\n{html_snippet}")
            
            logger.info("=== 页面结构分析完成 ===")
            
        except Exception as e:
            logger.error(f"页面结构分析失败: {e}")
    
    def try_advanced_checkbox_click(self):
        """尝试高级复选框点击方法"""
        try:
            logger.info("=== 开始高级复选框点击尝试 ===")
            
            # 方法1: 通过坐标点击
            try:
                logger.info("尝试方法1: 坐标点击")
                verify_text = self.driver.find_element(By.XPATH, "//*[contains(text(), 'Verify you are human')]")
                if verify_text.is_displayed():
                    # 获取文本元素的位置
                    location = verify_text.location
                    size = verify_text.size
                    
                    # 计算复选框可能的位置（通常在文本左侧）
                    checkbox_x = location['x'] - 30  # 假设复选框在文本左侧30像素
                    checkbox_y = location['y'] + size['height'] // 2
                    
                    logger.info(f"尝试点击坐标: ({checkbox_x}, {checkbox_y})")
                    
                    # 使用ActionChains点击坐标
                    actions = ActionChains(self.driver)
                    actions.move_by_offset(checkbox_x, checkbox_y).click().perform()
                    actions.move_by_offset(-checkbox_x, -checkbox_y)  # 重置鼠标位置
                    
                    time.sleep(2)
                    logger.info("坐标点击完成")
            except Exception as e:
                logger.info(f"坐标点击失败: {e}")
            
            # 方法2: JavaScript强制点击
            try:
                logger.info("尝试方法2: JavaScript强制点击")
                js_script = """
                // 查找所有复选框
                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                for (var i = 0; i < checkboxes.length; i++) {
                    var checkbox = checkboxes[i];
                    if (checkbox.offsetParent !== null) { // 检查是否可见
                        console.log('找到可见复选框:', checkbox);
                        checkbox.checked = true;
                        checkbox.click();
                        
                        // 触发change事件
                        var event = new Event('change', { bubbles: true });
                        checkbox.dispatchEvent(event);
                        
                        return '复选框已通过JavaScript选中';
                    }
                }
                return '未找到可见复选框';
                """
                result = self.driver.execute_script(js_script)
                logger.info(f"JavaScript执行结果: {result}")
                time.sleep(2)
            except Exception as e:
                logger.info(f"JavaScript点击失败: {e}")
            
            # 方法3: 查找父元素并点击
            try:
                logger.info("尝试方法3: 父元素点击")
                verify_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Verify you are human')]")
                for elem in verify_elements:
                    if elem.is_displayed():
                        # 尝试点击父元素
                        parent = elem.find_element(By.XPATH, "..")
                        logger.info(f"尝试点击父元素: {parent.tag_name}")
                        parent.click()
                        time.sleep(1)
                        
                        # 尝试点击祖父元素
                        grandparent = parent.find_element(By.XPATH, "..")
                        logger.info(f"尝试点击祖父元素: {grandparent.tag_name}")
                        grandparent.click()
                        time.sleep(1)
                        break
            except Exception as e:
                logger.info(f"父元素点击失败: {e}")
            
            # 方法4: 模拟键盘操作
            try:
                logger.info("尝试方法4: 键盘操作")
                from selenium.webdriver.common.keys import Keys
                
                # 尝试Tab到复选框并按空格
                body = self.driver.find_element(By.TAG_NAME, "body")
                for i in range(10):  # 尝试Tab 10次
                    body.send_keys(Keys.TAB)
                    time.sleep(0.5)
                    
                    # 检查当前焦点元素
                    active_element = self.driver.switch_to.active_element
                    if active_element.tag_name == "input" and active_element.get_attribute("type") == "checkbox":
                        logger.info("找到复选框，按空格键选中")
                        active_element.send_keys(Keys.SPACE)
                        time.sleep(1)
                        break
            except Exception as e:
                logger.info(f"键盘操作失败: {e}")
            
            # 检查是否成功
            try:
                checkbox = self.driver.find_element(By.CSS_SELECTOR, "input[type='checkbox']")
                if checkbox.is_selected():
                    logger.info("✅ 复选框已成功选中！")
                    return True
                else:
                    logger.info("❌ 复选框仍未选中")
                    return False
            except:
                logger.info("❌ 无法找到复选框来验证状态")
                return False
                
        except Exception as e:
            logger.error(f"高级复选框点击失败: {e}")
            return False
    
    def take_screenshot(self, filename):
        """截取屏幕截图"""
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"截图已保存: {filename}")
        except Exception as e:
            logger.error(f"截图失败: {e}")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    print("=== 复选框调试脚本 ===\n")
    
    if not print_config_status():
        print("\n请先修改 user_config.py 文件中的配置信息！")
        return
    
    print("这个脚本将详细分析页面结构并尝试多种方法点击复选框")
    
    confirm = input("\n是否开始调试？(y/n): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    bot = CheckboxDebugBot()
    
    try:
        print("\n🔍 开始调试流程...")
        
        # 步骤1: 访问页面并填写邮箱
        if not bot.navigate_and_fill_email():
            print("❌ 访问页面或填写邮箱失败")
            return
        
        # 步骤2: 分析页面结构
        bot.analyze_page_structure()
        bot.take_screenshot("debug_before_click.png")
        
        # 步骤3: 尝试高级点击方法
        success = bot.try_advanced_checkbox_click()
        bot.take_screenshot("debug_after_click.png")
        
        if success:
            print("✅ 复选框点击成功！")
        else:
            print("❌ 复选框点击失败，请查看日志和截图")
        
        # 等待用户查看
        input("\n按回车键继续...")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        bot.take_screenshot("debug_error.png")
        
    finally:
        bot.close()

if __name__ == "__main__":
    main()
