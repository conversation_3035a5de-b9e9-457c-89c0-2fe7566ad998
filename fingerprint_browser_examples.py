#!/usr/bin/env python3
"""
指纹浏览器使用示例
演示如何使用指纹浏览器管理系统
"""

import time
from fingerprint_browser_manager import FingerprintBrowserManager

def example_basic_usage():
    """基础使用示例"""
    print("🎯 基础使用示例")
    print("=" * 50)
    
    # 创建管理器
    manager = FingerprintBrowserManager("Example_Project")
    
    # 示例1: 创建简单浏览器
    print("\n1️⃣ 创建简单浏览器")
    config1 = {
        'username': 'test_user_1',
        'password': 'test_pass_1',
        'group': 'test_group',
        'url': 'https://www.google.com'
    }
    
    browser_id1 = manager.create_browser_profile('TestBrowser1', config1)
    if browser_id1:
        print("✅ 简单浏览器创建成功")
    
    # 示例2: 创建带代理的浏览器
    print("\n2️⃣ 创建带代理的浏览器")
    config2 = {
        'username': 'proxy_user',
        'password': 'proxy_pass',
        'group': 'proxy_group',
        'url': 'https://httpbin.org/ip',
        'proxy': {
            'type': 'http',
            'host': '127.0.0.1',
            'port': '8080',
            'username': '',
            'password': ''
        }
    }
    
    browser_id2 = manager.create_browser_profile('ProxyBrowser', config2)
    if browser_id2:
        print("✅ 代理浏览器创建成功")
    
    # 示例3: 创建自定义指纹浏览器
    print("\n3️⃣ 创建自定义指纹浏览器")
    config3 = {
        'username': 'fingerprint_user',
        'password': 'fingerprint_pass',
        'group': 'fingerprint_group',
        'url': 'https://www.whatismybrowser.com',
        'fingerprint': {
            'resolution': '1366x768',
            'timeZone': 'Asia/Shanghai',
            'languages': 'zh-CN,zh',
            'userAgent': '',  # 留空使用随机生成
            'webGL': '0',     # 启用WebGL指纹随机化
            'canvas': '0',    # 启用Canvas指纹随机化
            'hardwareConcurrency': '4',
            'deviceMemory': '8'
        }
    }
    
    browser_id3 = manager.create_browser_profile('FingerprintBrowser', config3)
    if browser_id3:
        print("✅ 指纹浏览器创建成功")
    
    # 列出所有浏览器
    print("\n📋 当前浏览器列表:")
    manager.list_browsers()
    
    return manager

def example_automation_usage():
    """自动化使用示例"""
    print("\n🤖 自动化使用示例")
    print("=" * 50)
    
    manager = FingerprintBrowserManager("Automation_Project")
    
    # 创建用于自动化的浏览器
    config = {
        'username': 'automation_user',
        'password': 'automation_pass',
        'group': 'automation',
        'url': 'https://app.augmentcode.com/account/subscription',
        'fingerprint': {
            'resolution': '1920x1080',
            'timeZone': 'America/New_York',
            'languages': 'en-US'
        }
    }
    
    browser_id = manager.create_browser_profile('AutomationBrowser', config)
    if not browser_id:
        print("❌ 浏览器创建失败")
        return
    
    # 打开浏览器
    print("\n🚀 打开浏览器进行自动化...")
    driver = manager.open_browser('AutomationBrowser')
    
    if driver:
        try:
            print("✅ 浏览器打开成功")
            print(f"📄 当前页面标题: {driver.title}")
            print(f"🌐 当前URL: {driver.current_url}")
            
            # 这里可以添加你的自动化逻辑
            # 例如：填写表单、点击按钮等
            
            # 等待一段时间让用户观察
            print("⏳ 等待5秒...")
            time.sleep(5)
            
        except Exception as e:
            print(f"❌ 自动化过程中出错: {e}")
        finally:
            # 关闭浏览器
            print("🔒 关闭浏览器...")
            manager.close_browser('AutomationBrowser')
    
    return manager

def example_batch_operations():
    """批量操作示例"""
    print("\n📦 批量操作示例")
    print("=" * 50)
    
    manager = FingerprintBrowserManager("Batch_Project")
    
    # 批量创建浏览器
    print("🔧 批量创建浏览器...")
    
    base_config = {
        'group': 'batch_test',
        'url': 'https://www.google.com',
        'fingerprint': {
            'resolution': '1920x1080',
            'languages': 'en-US'
        }
    }
    
    browser_names = []
    for i in range(1, 4):  # 创建3个浏览器
        name = f"BatchBrowser_{i:02d}"
        config = base_config.copy()
        config.update({
            'username': f'batch_user_{i:02d}',
            'password': f'batch_pass_{i:02d}'
        })
        
        browser_id = manager.create_browser_profile(name, config)
        if browser_id:
            browser_names.append(name)
            print(f"✅ 创建成功: {name}")
        else:
            print(f"❌ 创建失败: {name}")
        
        time.sleep(0.5)  # 避免请求过快
    
    # 列出创建的浏览器
    print("\n📋 批量创建的浏览器:")
    manager.list_browsers()
    
    # 批量打开浏览器
    print("\n🚀 批量打开浏览器...")
    drivers = {}
    for name in browser_names:
        driver = manager.open_browser(name)
        if driver:
            drivers[name] = driver
            print(f"✅ 打开成功: {name}")
        time.sleep(1)  # 避免同时打开太多
    
    # 排列窗口
    print("\n📐 排列窗口...")
    manager.arrange_windows('box', columns=2)
    
    # 等待观察
    print("⏳ 等待10秒让您观察窗口排列...")
    time.sleep(10)
    
    # 批量关闭
    print("\n🔒 批量关闭浏览器...")
    manager.close_all_browsers()
    
    return manager

def example_augment_code_automation():
    """Augment Code 自动化示例"""
    print("\n🎯 Augment Code 自动化示例")
    print("=" * 50)
    
    manager = FingerprintBrowserManager("AugmentCode_Project")
    
    # 创建专门用于Augment Code的浏览器
    config = {
        'username': '<EMAIL>',
        'password': 'Zui156997*',
        'group': 'augment_code',
        'url': 'https://app.augmentcode.com/account/subscription',
        'fingerprint': {
            'resolution': '1920x1080',
            'timeZone': 'America/New_York',
            'languages': 'en-US',
            'webGL': '0',
            'canvas': '0',
            'hardwareConcurrency': '8',
            'deviceMemory': '16'
        }
    }
    
    browser_id = manager.create_browser_profile('AugmentCodeBrowser', config)
    if not browser_id:
        print("❌ Augment Code浏览器创建失败")
        return
    
    # 打开浏览器
    print("\n🚀 打开Augment Code浏览器...")
    driver = manager.open_browser('AugmentCodeBrowser')
    
    if driver:
        try:
            print("✅ 浏览器打开成功")
            print(f"📄 当前页面标题: {driver.title}")
            print(f"🌐 当前URL: {driver.current_url}")
            
            # 这里可以集成之前的自动化脚本
            print("💡 提示: 可以在这里集成semi_auto_register.py的逻辑")
            print("💡 提示: 使用指纹浏览器可以避免检测，提高成功率")
            
            # 等待用户手动操作或自动化
            print("⏳ 浏览器已准备就绪，您可以手动操作或添加自动化逻辑...")
            input("按回车键关闭浏览器...")
            
        except Exception as e:
            print(f"❌ 过程中出错: {e}")
        finally:
            # 关闭浏览器
            print("🔒 关闭浏览器...")
            manager.close_browser('AugmentCodeBrowser')
    
    return manager

def main():
    """主函数 - 运行所有示例"""
    print("🌐 指纹浏览器管理系统 - 使用示例")
    print("=" * 70)
    
    try:
        # 基础使用示例
        manager1 = example_basic_usage()
        input("\n按回车键继续下一个示例...")
        
        # 自动化使用示例
        manager2 = example_automation_usage()
        input("\n按回车键继续下一个示例...")
        
        # 批量操作示例
        manager3 = example_batch_operations()
        input("\n按回车键继续下一个示例...")
        
        # Augment Code 自动化示例
        manager4 = example_augment_code_automation()
        
        print("\n🎉 所有示例演示完成!")
        print("\n💡 提示:")
        print("- 使用 python browser_cli.py 启动交互式界面")
        print("- 查看 README.md 了解更多功能")
        print("- 根据需要修改配置参数")
        
        # 清理所有创建的浏览器
        cleanup = input("\n是否清理所有示例浏览器? (y/n): ").strip().lower() == 'y'
        if cleanup:
            print("\n🧹 清理示例浏览器...")
            for manager in [manager1, manager2, manager3, manager4]:
                for name in list(manager.configs.keys()):
                    manager.delete_browser(name)
            print("✅ 清理完成")
        
    except KeyboardInterrupt:
        print("\n\n👋 示例被用户中断")
    except Exception as e:
        print(f"\n❌ 示例运行出错: {e}")

if __name__ == "__main__":
    main()
