# Augment Code 自动注册脚本使用说明

## 📋 功能概述

这套脚本可以帮您自动完成以下操作：
- ✅ 自动注册 Augment Code 账户
- ✅ 自动登录已有账户
- ✅ 登录后自动访问订阅页面
- ✅ 无痕模式浏览器操作
- ✅ 智能表单识别和填写
- ✅ 自动截图保存结果

## 🚀 快速开始

### 第一步：配置用户信息

编辑 `user_config.py` 文件，修改以下信息：

```python
USER_INFO = {
    "email": "<EMAIL>",  # 改为您的邮箱
    "password": "YourSecurePassword123!",  # 改为您的密码
    "username": "your_username",  # 改为您的用户名
    # ... 其他信息
}
```

### 第二步：运行脚本

```bash
python smart_auto_register.py
```

### 第三步：选择操作

脚本会显示菜单，选择您需要的操作：
1. 自动注册
2. 自动登录  
3. 登录后访问订阅页面
4. 仅打开浏览器（手动操作）

## 📁 文件说明

### 核心脚本文件

| 文件名 | 功能 | 推荐使用 |
|--------|------|----------|
| `smart_auto_register.py` | 智能自动注册脚本（推荐） | ⭐⭐⭐⭐⭐ |
| `auto_register.py` | 基础自动注册脚本 | ⭐⭐⭐ |
| `subscription_automation.py` | 订阅页面专用脚本 | ⭐⭐⭐ |
| `fixed_registration.py` | 修复版注册脚本 | ⭐⭐ |

### 配置文件

| 文件名 | 功能 |
|--------|------|
| `user_config.py` | 用户信息和网站配置 |
| `config.py` | 原始配置文件 |

### 其他文件

| 文件名 | 功能 |
|--------|------|
| `requirements.txt` | Python依赖包列表 |
| `setup.py` | 自动安装脚本 |
| `example_usage.py` | 使用示例 |

## ⚙️ 配置选项

### 用户信息配置

在 `user_config.py` 中配置：

```python
USER_INFO = {
    # 必填
    "email": "<EMAIL>",
    "password": "YourSecurePassword123!",
    
    # 可选
    "username": "your_username",
    "first_name": "Your",
    "last_name": "Name",
    "phone": "1234567890",
    "company": "Your Company"
}
```

### 浏览器配置

```python
BROWSER_CONFIG = {
    "chrome_path": r"E:\Google\Chrome\Application\chrome.exe",
    "headless": False,  # True=无头模式，False=显示浏览器
    "incognito": True,  # True=无痕模式
    "wait_timeout": 15,  # 等待超时时间
    "typing_delay": (0.05, 0.15)  # 打字延迟
}
```

### 网站URL配置

```python
WEBSITE_URLS = {
    "register": "https://app.augmentcode.com/register",
    "login": "https://app.augmentcode.com/login",
    "subscription": "https://app.augmentcode.com/account/subscription"
}
```

## 🔧 高级配置

### 自定义表单选择器

如果默认的表单识别不准确，可以在 `user_config.py` 中修改 `FORM_SELECTORS`：

```python
FORM_SELECTORS = {
    "email": [
        "input[type='email']",
        "input[name='email']",
        # 添加更多选择器...
    ],
    "password": [
        "input[type='password']",
        # 添加更多选择器...
    ]
}
```

## 🛠️ 故障排除

### 常见问题

1. **Chrome路径错误**
   ```
   错误：Chrome未找到
   解决：修改 user_config.py 中的 chrome_path
   ```

2. **表单填写失败**
   ```
   错误：未找到邮箱输入框
   解决：检查网站结构，更新表单选择器
   ```

3. **登录失败**
   ```
   错误：登录按钮点击失败
   解决：检查用户名密码是否正确
   ```

### 调试模式

1. 设置 `headless=False` 查看浏览器操作过程
2. 查看生成的截图文件了解执行结果
3. 检查控制台日志信息

## 📸 截图功能

脚本会自动保存截图：
- `register_result.png` - 注册结果
- `login_result.png` - 登录结果  
- `subscription_page.png` - 订阅页面
- `error.png` - 错误截图

## ⚠️ 注意事项

1. **合法使用**：请确保遵守网站服务条款
2. **账户安全**：不要在公共环境中运行脚本
3. **配置保密**：不要分享包含密码的配置文件
4. **网站变化**：如果网站结构改变，可能需要更新选择器

## 🔄 更新和维护

### 更新选择器

如果网站结构发生变化：
1. 使用浏览器开发者工具检查新的表单结构
2. 更新 `user_config.py` 中的选择器配置
3. 测试脚本功能

### 添加新功能

可以基于现有脚本扩展功能：
- 添加新的表单字段支持
- 增加更多网站支持
- 实现批量操作功能

## 📞 技术支持

如果遇到问题：
1. 检查配置文件是否正确
2. 查看错误日志和截图
3. 尝试手动操作验证网站功能
4. 更新浏览器和ChromeDriver版本

## 🎯 最佳实践

1. **首次使用**：先用"仅打开浏览器"模式熟悉网站
2. **测试配置**：使用测试账户验证脚本功能
3. **定期更新**：保持脚本和依赖包最新版本
4. **备份配置**：保存工作配置的备份

---

**祝您使用愉快！** 🎉
