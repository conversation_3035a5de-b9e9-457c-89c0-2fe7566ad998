# Augment 账号池管理系统

这是一个专门为管理多个 Augment Code 账号而设计的 Python 工具集，支持使用 Access Token 直接切换本地 Augment 账号登录。

## 🌟 主要功能

- ✅ **Access Token 直接切换**: 使用 Access Token 和 Tenant URL 直接切换账号
- ✅ **账号池管理**: 管理多个 Augment 账号配置
- ✅ **自动 VS Code 重启**: 切换账号后自动重启 VS Code 应用新配置
- ✅ **配置备份**: 自动备份当前配置，防止数据丢失
- ✅ **环境变量设置**: 自动设置系统环境变量
- ✅ **缓存管理**: 智能清除 Augment 缓存确保干净切换

## 📁 文件结构

```
├── augment_account_pool.py      # 完整的账号池管理系统
├── augment_token_switcher.py    # 轻量级 Token 切换器
├── quick_switch_example.py      # 快速切换示例和演示
├── augment_accounts.json        # 账号池配置文件
└── AUGMENT_ACCOUNT_POOL_README.md  # 本说明文件
```

## 🚀 快速开始

### 方法1: 使用快速切换示例（推荐新手）

```bash
python quick_switch_example.py
```

这会启动一个友好的菜单界面，包含：
- 立即切换到你的账号
- 设置账号池
- 创建配置文件
- 查看当前状态

### 方法2: 使用完整账号池管理

```bash
python augment_account_pool.py
```

提供完整的账号管理功能：
- 添加/删除账号
- 切换账号
- 导入/导出配置
- 备份管理

### 方法3: 使用轻量级 Token 切换器

```bash
python augment_token_switcher.py
```

专注于 Token 切换功能：
- 快速切换 Token
- 管理 Token 配置文件
- 清除缓存
- 查看当前状态

## 💡 使用你的 Access Token

你已经获得了以下信息：
- **Access Token**: `aaabd8f0d4ab1233d05139a2f483f5a3ad33d3dc63586f5624b6aae7b8c0c77f`
- **Tenant URL**: `https://d5.api.augmentcode.com/`

### 立即切换到你的账号

1. 运行快速切换示例：
   ```bash
   python quick_switch_example.py
   ```

2. 选择选项 `1` - "立即切换到你的账号"

3. 确认切换，系统会：
   - 设置 VS Code 配置
   - 设置环境变量
   - 清除旧缓存
   - 重启 VS Code

## 🔧 详细功能说明

### 账号池管理 (augment_account_pool.py)

**主要功能：**
- 添加账号到池中
- 列出所有账号
- 切换到指定账号
- 删除账号
- 查看当前使用的账号
- 导出/导入账号池

**使用示例：**
```python
from augment_account_pool import AugmentAccountPool

pool = AugmentAccountPool()

# 添加账号
pool.add_account(
    name="我的账号",
    access_token="your_token_here",
    tenant_url="https://d5.api.augmentcode.com/",
    email="<EMAIL>"
)

# 切换账号
pool.switch_account("我的账号")
```

### Token 切换器 (augment_token_switcher.py)

**主要功能：**
- 快速切换 Token
- 创建和管理 Token 配置文件
- 清除 Augment 缓存
- 查看当前 Token 信息
- 重启 VS Code 扩展

**使用示例：**
```python
from augment_token_switcher import AugmentTokenSwitcher

switcher = AugmentTokenSwitcher()

# 快速切换
switcher.switch_token_quick(
    access_token="your_token",
    tenant_url="https://d5.api.augmentcode.com/",
    clear_cache=True,
    restart=True
)
```

## 🛠️ 工作原理

### 1. VS Code 设置修改
系统会修改 `%APPDATA%\Code\User\settings.json` 文件，添加：
```json
{
  "augment.apiUrl": "https://d5.api.augmentcode.com/",
  "augment.accessToken": "your_token_here",
  "augment.tenantUrl": "https://d5.api.augmentcode.com/"
}
```

### 2. 环境变量设置
设置以下环境变量：
- `AUGMENT_ACCESS_TOKEN`
- `AUGMENT_TENANT_URL`

### 3. 缓存管理
清除 `%APPDATA%\Code\User\globalStorage\augment.vscode-augment` 目录下的缓存文件

### 4. VS Code 重启
终止现有 VS Code 进程并重新启动，确保新配置生效

## 📋 配置文件格式

### 账号池配置 (augment_accounts.json)
```json
{
  "accounts": [
    {
      "name": "账号名称",
      "access_token": "token_string",
      "tenant_url": "https://d5.api.augmentcode.com/",
      "email": "<EMAIL>",
      "description": "账号描述",
      "created_at": "2025-07-29 06:30:00",
      "last_used": "2025-07-29 07:00:00"
    }
  ],
  "current_account": "当前使用的账号名称"
}
```

### Token 配置文件 (augment_profiles/*.json)
```json
{
  "name": "配置名称",
  "access_token": "token_string",
  "tenant_url": "https://d5.api.augmentcode.com/",
  "email": "<EMAIL>",
  "description": "配置描述",
  "created_at": "2025-07-29 06:30:00"
}
```

## 🔒 安全注意事项

1. **Token 安全**: Access Token 包含敏感信息，请妥善保管
2. **文件权限**: 确保配置文件只有你能访问
3. **备份重要**: 系统会自动备份，但建议定期手动备份重要配置
4. **网络安全**: 确保 Tenant URL 是可信的

## 🐛 故障排除

### 常见问题

**1. VS Code 没有重启**
- 手动关闭所有 VS Code 窗口
- 运行 `code .` 重新打开

**2. Token 没有生效**
- 检查 VS Code 设置文件是否正确更新
- 清除 Augment 缓存后重试
- 确认 Token 和 URL 格式正确

**3. 环境变量没有设置**
- 重启命令行窗口
- 检查系统环境变量设置

**4. 权限问题**
- 以管理员身份运行脚本
- 检查文件和目录权限

### 调试模式

在脚本中启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📞 支持

如果遇到问题：
1. 检查日志输出
2. 确认 VS Code 和 Augment 扩展版本
3. 验证 Token 和 URL 的有效性
4. 查看备份文件是否完整

## 🎯 最佳实践

1. **定期备份**: 使用导出功能定期备份账号池
2. **命名规范**: 使用有意义的账号名称和描述
3. **安全存储**: 不要在公共场所存储包含 Token 的文件
4. **测试切换**: 在重要工作前测试账号切换功能
5. **清理缓存**: 定期清理 Augment 缓存保持系统整洁

---

**享受使用 Augment 账号池管理系统！** 🎉
