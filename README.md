# 网站注册自动化脚本

这是一个用于自动化网站注册流程的Python脚本，使用Selenium WebDriver来模拟浏览器操作。

## ⚠️ 重要声明

**请确保您的使用符合以下要求：**
- 遵守目标网站的服务条款
- 不违反反垃圾邮件政策
- 尊重网站的速率限制
- 仅用于合法目的
- 获得网站所有者的许可（如适用）

## 功能特性

- 🤖 自动填写注册表单
- 🔄 支持多种表单字段选择器
- 🎭 模拟人类行为（随机延迟、打字速度）
- 📊 详细的日志记录
- 🔧 可配置的设置
- 🚀 支持批量注册
- 💻 交互式注册模式
- 🕶️ 支持无头模式

## 安装要求

### 系统要求
- Python 3.7+
- Chrome浏览器

### 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. **复制环境变量文件：**
   ```bash
   cp .env.example .env
   ```

2. **编辑 `.env` 文件，填入您的注册信息：**
   ```
   REGISTRATION_EMAIL=<EMAIL>
   REGISTRATION_PASSWORD=your_secure_password
   REGISTRATION_USERNAME=your_username
   ```

3. **修改 `config.py` 中的网站URL：**
   ```python
   REGISTRATION_URL = "https://your-target-website.com/register"
   ```

4. **根据目标网站调整表单选择器：**
   在 `config.py` 中的 `SELECTORS` 字典中添加或修改选择器。

## 使用方法

### 方法1：使用示例脚本（推荐）

```bash
python example_usage.py
```

这将显示一个菜单，您可以选择：
- 单个账户注册
- 批量账户注册
- 交互式注册

### 方法2：直接使用核心类

```python
from registration_automation import RegistrationBot

# 用户数据
user_data = {
    'email': '<EMAIL>',
    'password': 'SecurePassword123!',
    'confirm_password': 'SecurePassword123!',
    'username': 'test_user'
}

# 创建机器人实例
bot = RegistrationBot(headless=False)

try:
    # 执行注册
    success = bot.register("https://example.com/register", user_data)
    if success:
        print("注册成功！")
    else:
        print("注册失败")
finally:
    bot.close()
```

## 配置选项

### 主要配置项（config.py）

| 配置项 | 描述 | 默认值 |
|--------|------|--------|
| `HEADLESS_MODE` | 是否使用无头模式 | `False` |
| `WAIT_TIMEOUT` | 等待超时时间（秒） | `10` |
| `MIN_DELAY` | 最小延迟（秒） | `1` |
| `MAX_DELAY` | 最大延迟（秒） | `3` |
| `MAX_RETRIES` | 最大重试次数 | `3` |

### 表单选择器配置

在 `config.py` 的 `SELECTORS` 字典中配置表单字段的CSS选择器：

```python
SELECTORS = {
    'email': [
        "input[type='email']",
        "input[name='email']",
        "#email"
    ],
    'password': [
        "input[type='password']",
        "input[name='password']",
        "#password"
    ],
    # ... 更多字段
}
```

## 故障排除

### 常见问题

1. **ChromeDriver 问题**
   - 脚本会自动下载和管理ChromeDriver
   - 确保Chrome浏览器已安装

2. **元素找不到**
   - 检查 `config.py` 中的选择器是否正确
   - 使用浏览器开发者工具检查实际的HTML结构

3. **验证码问题**
   - 脚本会暂停并等待手动完成验证码
   - 可以集成第三方验证码解决服务

4. **网站反爬虫机制**
   - 调整延迟设置
   - 使用不同的用户代理
   - 考虑使用代理服务器

### 调试技巧

1. **启用详细日志：**
   ```python
   LOG_LEVEL = "DEBUG"
   ```

2. **禁用无头模式查看浏览器操作：**
   ```python
   HEADLESS_MODE = False
   ```

3. **增加等待时间：**
   ```python
   WAIT_TIMEOUT = 30
   ```

## 文件结构

```
.
├── registration_automation.py  # 核心自动化类
├── config.py                  # 配置文件
├── example_usage.py           # 使用示例
├── requirements.txt           # 依赖列表
├── .env.example              # 环境变量示例
├── README.md                 # 说明文档
└── registration.log          # 日志文件（运行后生成）
```

## 扩展功能

### 添加新的表单字段

1. 在 `config.py` 的 `SELECTORS` 中添加新字段的选择器
2. 在 `RegistrationBot.fill_registration_form()` 方法中添加处理逻辑

### 集成验证码服务

可以集成如2captcha、Anti-Captcha等服务来自动解决验证码。

### 添加代理支持

在Chrome选项中添加代理配置：

```python
chrome_options.add_argument('--proxy-server=http://proxy:port')
```

## 许可证

本项目仅供学习和研究目的使用。使用者需要确保遵守相关法律法规和网站服务条款。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 免责声明

本工具仅供教育和研究目的。使用者需要自行承担使用风险，并确保遵守相关法律法规。开发者不对任何滥用行为负责。
