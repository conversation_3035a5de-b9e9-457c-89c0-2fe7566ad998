#!/usr/bin/env python3
"""
专门针对订阅页面的自动注册脚本
处理需要先访问订阅页面才能注册的情况
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 导入配置
from user_config import (
    USER_INFO, WEBSITE_URLS, BROWSER_CONFIG, 
    FORM_SELECTORS, BUTTON_SELECTORS, LINK_SELECTORS,
    validate_config, print_config_status
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SubscriptionRegisterBot:
    def __init__(self, headless=None):
        """初始化订阅页面注册机器人"""
        self.driver = None
        self.wait = None
        
        if headless is None:
            headless = BROWSER_CONFIG["headless"]
            
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")
            
            chrome_options.binary_location = BROWSER_CONFIG["chrome_path"]
            
            if BROWSER_CONFIG["incognito"]:
                chrome_options.add_argument("--incognito")
                chrome_options.add_argument("--new-window")
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            chrome_options.add_experimental_option("prefs", {
                "credentials_enable_service": False,
                "profile.password_manager_enabled": False,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0
            })
            
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, BROWSER_CONFIG["wait_timeout"])
            
            self.driver.maximize_window()
            
            logger.info("Chrome WebDriver 初始化成功")
            
        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def navigate_to_page(self, url):
        """导航到指定页面"""
        try:
            logger.info(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(3)
            
            page_title = self.driver.title
            current_url = self.driver.current_url
            logger.info(f"页面标题: {page_title}")
            logger.info(f"当前URL: {current_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"无法访问页面: {e}")
            return False
    
    def analyze_page(self):
        """分析当前页面内容"""
        try:
            logger.info("正在分析页面内容...")
            
            # 检查页面标题
            title = self.driver.title.lower()
            url = self.driver.current_url.lower()
            
            logger.info(f"页面分析 - 标题: {title}")
            logger.info(f"页面分析 - URL: {url}")
            
            # 查找所有输入框
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            logger.info(f"找到 {len(inputs)} 个输入框")
            
            for i, input_elem in enumerate(inputs):
                if input_elem.is_displayed():
                    input_type = input_elem.get_attribute("type")
                    input_name = input_elem.get_attribute("name")
                    input_id = input_elem.get_attribute("id")
                    input_placeholder = input_elem.get_attribute("placeholder")
                    logger.info(f"  输入框 {i+1}: type={input_type}, name={input_name}, id={input_id}, placeholder={input_placeholder}")
            
            # 查找所有按钮
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            logger.info(f"找到 {len(buttons)} 个按钮")
            
            for i, button in enumerate(buttons):
                if button.is_displayed():
                    button_text = button.text
                    button_type = button.get_attribute("type")
                    logger.info(f"  按钮 {i+1}: text='{button_text}', type={button_type}")
            
            # 查找所有链接
            links = self.driver.find_elements(By.TAG_NAME, "a")
            register_links = []
            for link in links:
                if link.is_displayed():
                    link_text = link.text.lower()
                    link_href = link.get_attribute("href")
                    if any(keyword in link_text for keyword in ["sign", "register", "login", "注册", "登录"]):
                        register_links.append({
                            "text": link.text,
                            "href": link_href
                        })
            
            if register_links:
                logger.info(f"找到 {len(register_links)} 个相关链接:")
                for link in register_links:
                    logger.info(f"  - {link['text']}: {link['href']}")
            
            return {
                "title": title,
                "url": url,
                "inputs": len(inputs),
                "buttons": len(buttons),
                "register_links": register_links
            }
            
        except Exception as e:
            logger.error(f"页面分析失败: {e}")
            return None
    
    def type_slowly(self, element, text):
        """模拟人类打字速度"""
        element.clear()
        delay_range = BROWSER_CONFIG["typing_delay"]
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(*delay_range))
    
    def find_and_fill_field(self, field_type, value):
        """查找并填写指定类型的字段"""
        selectors = FORM_SELECTORS.get(field_type, [])
        
        for selector in selectors:
            try:
                field = self.driver.find_element(By.CSS_SELECTOR, selector)
                if field.is_displayed():
                    self.type_slowly(field, value)
                    logger.info(f"{field_type}填写成功: {selector}")
                    return True
            except:
                continue
        
        # 如果CSS选择器失败，尝试通过属性查找
        try:
            # 通过placeholder查找
            if field_type == "email":
                xpath_selectors = [
                    "//input[contains(@placeholder, 'email')]",
                    "//input[contains(@placeholder, 'Email')]",
                    "//input[@type='email']"
                ]
            elif field_type == "password":
                xpath_selectors = [
                    "//input[@type='password']",
                    "//input[contains(@placeholder, 'password')]",
                    "//input[contains(@placeholder, 'Password')]"
                ]
            else:
                xpath_selectors = []
            
            for xpath in xpath_selectors:
                try:
                    field = self.driver.find_element(By.XPATH, xpath)
                    if field.is_displayed():
                        self.type_slowly(field, value)
                        logger.info(f"{field_type}填写成功: {xpath}")
                        return True
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"XPath查找失败: {e}")
        
        logger.warning(f"未找到{field_type}输入框")
        return False
    
    def click_button_by_text(self, button_texts):
        """通过按钮文本点击按钮"""
        for text in button_texts:
            try:
                # 尝试精确匹配
                xpath = f"//button[text()='{text}']"
                button = self.driver.find_element(By.XPATH, xpath)
                if button.is_displayed():
                    self.driver.execute_script("arguments[0].scrollIntoView();", button)
                    time.sleep(1)
                    button.click()
                    logger.info(f"按钮点击成功: {text}")
                    return True
            except:
                try:
                    # 尝试包含匹配
                    xpath = f"//button[contains(text(), '{text}')]"
                    button = self.driver.find_element(By.XPATH, xpath)
                    if button.is_displayed():
                        self.driver.execute_script("arguments[0].scrollIntoView();", button)
                        time.sleep(1)
                        button.click()
                        logger.info(f"按钮点击成功: {text}")
                        return True
                except:
                    continue
        
        logger.warning(f"未找到按钮: {button_texts}")
        return False
    
    def handle_subscription_page(self):
        """处理订阅页面的注册流程"""
        try:
            logger.info("开始处理订阅页面...")
            
            # 1. 访问订阅页面
            if not self.navigate_to_page(WEBSITE_URLS["subscription"]):
                return False
            
            # 2. 分析页面内容
            page_info = self.analyze_page()
            if not page_info:
                return False
            
            # 3. 检查是否需要登录
            if "login" in page_info["url"] or "sign" in page_info["url"]:
                logger.info("检测到需要登录，尝试查找注册选项...")
                
                # 查找注册链接
                register_texts = ["Sign up", "Register", "Create account", "注册", "创建账户"]
                for text in register_texts:
                    try:
                        xpath = f"//a[contains(text(), '{text}')]"
                        link = self.driver.find_element(By.XPATH, xpath)
                        if link.is_displayed():
                            link.click()
                            logger.info(f"注册链接点击成功: {text}")
                            time.sleep(3)
                            break
                    except:
                        continue
                
                # 重新分析页面
                page_info = self.analyze_page()
            
            # 4. 尝试填写表单
            success_count = 0
            
            # 填写邮箱
            if self.find_and_fill_field("email", USER_INFO["email"]):
                success_count += 1
            
            # 填写密码
            if self.find_and_fill_field("password", USER_INFO["password"]):
                success_count += 1
            
            # 填写用户名（如果有）
            if USER_INFO["username"] and USER_INFO["username"] != "your_username":
                self.find_and_fill_field("username", USER_INFO["username"])
            
            # 填写确认密码
            confirm_password = USER_INFO["confirm_password"] or USER_INFO["password"]
            self.find_and_fill_field("confirm_password", confirm_password)
            
            if success_count < 1:
                logger.error("关键字段填写失败")
                return False
            
            # 5. 尝试提交表单
            submit_texts = ["Sign up", "Register", "Create account", "Submit", "注册", "提交"]
            if self.click_button_by_text(submit_texts):
                logger.info("表单提交成功")
                time.sleep(5)
                return True
            else:
                logger.error("未找到提交按钮")
                return False
            
        except Exception as e:
            logger.error(f"处理订阅页面失败: {e}")
            return False
    
    def take_screenshot(self, filename):
        """截取屏幕截图"""
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"截图已保存: {filename}")
            return True
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False
    
    def wait_for_user(self, seconds=30):
        """等待用户操作"""
        logger.info(f"等待用户操作 {seconds} 秒...")
        time.sleep(seconds)
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("=== 订阅页面自动注册脚本 ===\n")
    
    # 检查配置
    if not print_config_status():
        print("\n请先修改 user_config.py 文件中的配置信息！")
        return
    
    print(f"\n目标页面: {WEBSITE_URLS['subscription']}")
    print("这个脚本会智能分析页面内容并尝试自动注册")
    
    confirm = input("\n是否继续？(y/n): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    bot = SubscriptionRegisterBot()
    
    try:
        print("\n开始自动注册流程...")
        success = bot.handle_subscription_page()
        
        if success:
            print("✅ 注册流程完成")
            bot.take_screenshot("subscription_register_result.png")
        else:
            print("❌ 注册失败")
            bot.take_screenshot("subscription_register_error.png")
        
        # 等待用户查看结果
        bot.wait_for_user(60)
        
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        bot.take_screenshot("subscription_register_error.png")
        
    finally:
        input("\n按回车键关闭浏览器...")
        bot.close()

if __name__ == "__main__":
    main()
