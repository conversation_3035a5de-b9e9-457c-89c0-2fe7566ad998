#!/usr/bin/env python3
"""
Augment Code 专用自动注册脚本
专门处理 Augment Code 的登录/注册流程，包括人机验证
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 导入配置
from user_config import (
    USER_INFO, WEBSITE_URLS, BROWSER_CONFIG, 
    validate_config, print_config_status
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AugmentAutoRegisterBot:
    def __init__(self, headless=None):
        """初始化 Augment Code 专用注册机器人"""
        self.driver = None
        self.wait = None
        
        if headless is None:
            headless = BROWSER_CONFIG["headless"]
            
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")
            
            chrome_options.binary_location = BROWSER_CONFIG["chrome_path"]
            
            if BROWSER_CONFIG["incognito"]:
                chrome_options.add_argument("--incognito")
                chrome_options.add_argument("--new-window")
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            chrome_options.add_experimental_option("prefs", {
                "credentials_enable_service": False,
                "profile.password_manager_enabled": False,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0
            })
            
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, BROWSER_CONFIG["wait_timeout"])
            
            self.driver.maximize_window()
            
            logger.info("Chrome WebDriver 初始化成功")
            
        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def navigate_to_subscription(self):
        """访问订阅页面"""
        try:
            logger.info(f"正在访问订阅页面: {WEBSITE_URLS['subscription']}")
            self.driver.get(WEBSITE_URLS['subscription'])
            time.sleep(3)
            
            page_title = self.driver.title
            current_url = self.driver.current_url
            logger.info(f"页面标题: {page_title}")
            logger.info(f"当前URL: {current_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"无法访问订阅页面: {e}")
            return False
    
    def type_slowly(self, element, text):
        """模拟人类打字速度"""
        element.clear()
        delay_range = BROWSER_CONFIG["typing_delay"]
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(*delay_range))
    
    def fill_email(self, email):
        """填写邮箱地址"""
        try:
            logger.info("正在填写邮箱地址...")
            
            # 等待邮箱输入框出现
            email_selectors = [
                "input[name='username']",
                "input[type='email']",
                "input[name='email']",
                "input[id='email']",
                "input[id='username']"
            ]
            
            for selector in email_selectors:
                try:
                    email_field = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    
                    if email_field.is_displayed():
                        self.type_slowly(email_field, email)
                        logger.info(f"邮箱填写成功: {email}")
                        return True
                except:
                    continue
            
            logger.error("未找到邮箱输入框")
            return False
            
        except Exception as e:
            logger.error(f"填写邮箱失败: {e}")
            return False
    
    def handle_human_verification(self, max_wait=30):
        """处理人机验证"""
        try:
            logger.info("检查并处理人机验证...")

            # 首先等待页面稳定
            time.sleep(2)

            # 查找人机验证复选框的多种方式
            verification_strategies = [
                # 策略1: 直接查找复选框
                {
                    "name": "直接复选框查找",
                    "selectors": [
                        "input[type='checkbox']",
                        "//input[@type='checkbox']",
                        "input[type='checkbox'][class*='checkbox']"
                    ]
                },
                # 策略2: 通过文本查找相关复选框
                {
                    "name": "文本关联复选框查找",
                    "selectors": [
                        "//div[contains(text(), 'Verify you are human')]//input[@type='checkbox']",
                        "//label[contains(text(), 'Verify you are human')]//input[@type='checkbox']",
                        "//span[contains(text(), 'Verify you are human')]//input[@type='checkbox']",
                        "//div[contains(text(), 'Verify you are human')]/preceding-sibling::input[@type='checkbox']",
                        "//div[contains(text(), 'Verify you are human')]/following-sibling::input[@type='checkbox']"
                    ]
                },
                # 策略3: 查找验证区域并点击
                {
                    "name": "验证区域点击",
                    "selectors": [
                        "//div[contains(text(), 'Verify you are human')]",
                        "//label[contains(text(), 'Verify you are human')]",
                        "//span[contains(text(), 'Verify you are human')]"
                    ]
                }
            ]

            for strategy in verification_strategies:
                logger.info(f"尝试策略: {strategy['name']}")

                for selector in strategy["selectors"]:
                    try:
                        if selector.startswith("//"):
                            element = self.driver.find_element(By.XPATH, selector)
                        else:
                            element = self.driver.find_element(By.CSS_SELECTOR, selector)

                        if element.is_displayed():
                            # 滚动到元素位置
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                            time.sleep(1)

                            # 如果是复选框，检查是否已选中
                            if element.tag_name == "input" and element.get_attribute("type") == "checkbox":
                                if not element.is_selected():
                                    logger.info(f"找到未选中的复选框，准备点击: {selector}")

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1: 直接点击
                                        element.click()
                                        logger.info("复选框点击成功 (直接点击)")
                                    except:
                                        try:
                                            # 方式2: JavaScript点击
                                            self.driver.execute_script("arguments[0].click();", element)
                                            logger.info("复选框点击成功 (JavaScript点击)")
                                        except:
                                            # 方式3: 模拟鼠标点击
                                            from selenium.webdriver.common.action_chains import ActionChains
                                            ActionChains(self.driver).move_to_element(element).click().perform()
                                            logger.info("复选框点击成功 (ActionChains点击)")

                                    # 等待验证完成
                                    time.sleep(3)

                                    # 验证是否成功选中
                                    if element.is_selected():
                                        logger.info("✅ 人机验证复选框已成功选中")
                                        return True
                                    else:
                                        logger.warning("复选框点击后仍未选中，继续尝试其他方式")
                                else:
                                    logger.info("复选框已经选中")
                                    return True
                            else:
                                # 如果不是复选框，尝试点击验证区域
                                logger.info(f"尝试点击验证区域: {selector}")
                                try:
                                    element.click()
                                    logger.info("验证区域点击成功")
                                    time.sleep(3)
                                    return True
                                except:
                                    self.driver.execute_script("arguments[0].click();", element)
                                    logger.info("验证区域点击成功 (JavaScript)")
                                    time.sleep(3)
                                    return True

                    except Exception as e:
                        logger.debug(f"选择器 {selector} 失败: {e}")
                        continue

            # 如果所有策略都失败，等待用户手动操作
            logger.warning("自动人机验证失败，等待用户手动操作...")
            for i in range(10):
                time.sleep(1)
                try:
                    checkbox = self.driver.find_element(By.CSS_SELECTOR, "input[type='checkbox']")
                    if checkbox.is_selected():
                        logger.info("检测到用户已手动完成验证")
                        return True
                except:
                    pass
                logger.info(f"等待手动验证... ({i+1}/10)")

            logger.error("人机验证处理失败")
            return False

        except Exception as e:
            logger.error(f"处理人机验证失败: {e}")
            return False
    
    def click_continue(self):
        """点击Continue按钮"""
        try:
            logger.info("正在点击Continue按钮...")
            
            # 等待按钮可点击
            continue_selectors = [
                "button:contains('Continue')",
                "//button[text()='Continue']",
                "//button[contains(text(), 'Continue')]",
                "button[type='submit']"
            ]
            
            for selector in continue_selectors:
                try:
                    if selector.startswith("//"):
                        button = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        if ":contains(" in selector:
                            # 转换为XPath
                            text = "Continue"
                            xpath = f"//button[contains(text(), '{text}')]"
                            button = WebDriverWait(self.driver, 10).until(
                                EC.element_to_be_clickable((By.XPATH, xpath))
                            )
                        else:
                            button = WebDriverWait(self.driver, 10).until(
                                EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                            )
                    
                    if button.is_displayed() and button.is_enabled():
                        self.driver.execute_script("arguments[0].scrollIntoView();", button)
                        time.sleep(1)
                        button.click()
                        logger.info("Continue按钮点击成功")
                        return True
                except:
                    continue
            
            logger.error("未找到可点击的Continue按钮")
            return False
            
        except Exception as e:
            logger.error(f"点击Continue按钮失败: {e}")
            return False
    
    def complete_augment_flow(self):
        """完成Augment Code的完整流程"""
        try:
            logger.info("开始Augment Code自动注册流程...")
            
            # 步骤1: 访问订阅页面
            if not self.navigate_to_subscription():
                return False
            
            # 步骤2: 填写邮箱
            if not self.fill_email(USER_INFO["email"]):
                return False
            
            # 步骤3: 处理人机验证
            if not self.handle_human_verification():
                return False
            
            # 步骤4: 点击Continue按钮
            if not self.click_continue():
                return False
            
            # 步骤5: 等待页面跳转
            logger.info("等待页面跳转...")
            time.sleep(5)
            
            # 步骤6: 检查结果
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            logger.info(f"流程完成后的页面标题: {page_title}")
            logger.info(f"流程完成后的URL: {current_url}")
            
            if "password" in current_url.lower() or "sign" in page_title.lower():
                logger.info("✅ 成功进入下一步！可能需要设置密码或已进入登录流程")
                return True
            elif "dashboard" in current_url.lower() or "subscription" in current_url.lower():
                logger.info("✅ 可能已经成功登录或注册！")
                return True
            else:
                logger.info("✅ 流程已完成，请查看当前页面状态")
                return True
            
        except Exception as e:
            logger.error(f"Augment Code流程失败: {e}")
            return False
    
    def take_screenshot(self, filename):
        """截取屏幕截图"""
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"截图已保存: {filename}")
            return True
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False
    
    def wait_for_user(self, seconds=60):
        """等待用户操作"""
        logger.info(f"等待用户查看结果 {seconds} 秒...")
        time.sleep(seconds)
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("=== Augment Code 专用自动注册脚本 ===\n")
    
    # 检查配置
    if not print_config_status():
        print("\n请先修改 user_config.py 文件中的配置信息！")
        return
    
    print(f"\n目标邮箱: {USER_INFO['email']}")
    print(f"目标页面: {WEBSITE_URLS['subscription']}")
    print("\n这个脚本专门处理 Augment Code 的登录/注册流程")
    print("包括：邮箱填写 → 人机验证 → Continue按钮点击")
    
    confirm = input("\n是否开始自动注册流程？(y/n): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    bot = AugmentAutoRegisterBot()
    
    try:
        print("\n🚀 开始自动注册流程...")
        success = bot.complete_augment_flow()
        
        if success:
            print("✅ 自动注册流程完成！")
            bot.take_screenshot("augment_register_success.png")
        else:
            print("❌ 自动注册流程失败")
            bot.take_screenshot("augment_register_error.png")
        
        # 等待用户查看结果
        bot.wait_for_user(60)
        
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        bot.take_screenshot("augment_register_error.png")
        
    finally:
        input("\n按回车键关闭浏览器...")
        bot.close()

if __name__ == "__main__":
    main()
