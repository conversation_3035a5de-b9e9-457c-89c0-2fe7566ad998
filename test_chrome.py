#!/usr/bin/env python3
"""
测试Chrome浏览器路径和WebDriver设置
"""

import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def test_chrome_paths():
    """测试Chrome浏览器路径"""
    print("=== 测试Chrome浏览器路径 ===")
    
    # 可能的Chrome路径
    possible_paths = [
        r"E:\Google\Chrome\Application\chrome.exe",  # 用户指定的路径
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
    ]
    
    found_paths = []
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到Chrome: {path}")
            found_paths.append(path)
        else:
            print(f"❌ 未找到: {path}")
    
    return found_paths

def test_webdriver_setup(chrome_path=None):
    """测试WebDriver设置"""
    print(f"\n=== 测试WebDriver设置 ===")
    
    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 使用无头模式测试
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        if chrome_path:
            chrome_options.binary_location = chrome_path
            print(f"使用Chrome路径: {chrome_path}")
        
        # 获取ChromeDriver
        service = Service(ChromeDriverManager().install())
        
        # 创建WebDriver实例
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 测试访问网页
        driver.get("https://www.google.com")
        title = driver.title
        print(f"✅ WebDriver测试成功！页面标题: {title}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ WebDriver测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Chrome浏览器和WebDriver测试工具\n")
    
    # 测试Chrome路径
    chrome_paths = test_chrome_paths()
    
    if not chrome_paths:
        print("\n❌ 未找到任何Chrome浏览器安装")
        print("请确保Chrome浏览器已正确安装")
        return
    
    # 测试WebDriver
    for chrome_path in chrome_paths:
        print(f"\n正在测试Chrome路径: {chrome_path}")
        if test_webdriver_setup(chrome_path):
            print(f"✅ 成功！可以使用路径: {chrome_path}")
            break
    else:
        print("\n❌ 所有Chrome路径测试都失败了")
        print("尝试不指定Chrome路径...")
        if test_webdriver_setup():
            print("✅ 使用系统默认Chrome路径成功")
        else:
            print("❌ 所有测试都失败了")

if __name__ == "__main__":
    main()
