#!/usr/bin/env python3
"""
指纹浏览器系统安装和检查脚本
检查系统环境并安装必要的依赖
"""

import os
import sys
import subprocess
import json
import requests
from pathlib import Path

def print_banner():
    """显示安装横幅"""
    print("=" * 70)
    print("🌐 指纹浏览器管理系统 - 安装向导")
    print("=" * 70)

def check_python_version():
    """检查Python版本"""
    print("\n🐍 检查Python版本...")
    
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.7或更高版本")
        return False

def install_python_packages():
    """安装Python依赖包"""
    print("\n📦 安装Python依赖包...")
    
    packages = [
        'selenium',
        'requests',
        'faker'
    ]
    
    for package in packages:
        try:
            print(f"  安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"  ✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"  ❌ {package} 安装失败")
            return False
    
    print("✅ 所有Python依赖包安装完成")
    return True

def check_chrome_browser():
    """检查Chrome浏览器"""
    print("\n🌐 检查Chrome浏览器...")
    
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"E:\Google\Chrome\Application\chrome.exe",  # 用户自定义路径
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ 找到Chrome浏览器: {path}")
            return True
    
    print("❌ 未找到Chrome浏览器")
    print("   请从 https://www.google.com/chrome/ 下载并安装Chrome")
    return False

def check_bit_browser():
    """检查比特浏览器"""
    print("\n🔧 检查比特浏览器...")
    
    # 检查配置文件
    config_paths = []
    
    try:
        # 当前用户路径
        user_path = fr'C:\Users\<USER>\AppData\Roaming\bitbrowser\config.json'
        config_paths.append(user_path)
        
        # 枚举所有用户
        users_dir = Path('C:/Users')
        if users_dir.exists():
            for user_dir in users_dir.iterdir():
                if user_dir.is_dir():
                    config_path = user_dir / 'AppData/Roaming/bitbrowser/config.json'
                    config_paths.append(str(config_path))
    except:
        pass
    
    for config_path in config_paths:
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                server_address = config.get('localServerAddress', '')
                if server_address:
                    print(f"✅ 找到比特浏览器配置: {config_path}")
                    print(f"   服务器地址: {server_address}")
                    
                    # 测试连接
                    try:
                        port = server_address.split(':')[-1]
                        test_url = f'http://127.0.0.1:{port}'
                        response = requests.get(test_url, timeout=5)
                        if response.status_code == 200:
                            print("✅ 比特浏览器服务正在运行")
                            return True
                        else:
                            print("⚠️ 比特浏览器服务未响应")
                    except:
                        print("⚠️ 比特浏览器服务连接失败")
                    
                    return True
            except:
                continue
    
    print("❌ 未找到比特浏览器或配置文件")
    print("   请从 https://www.bitbrowser.cn/ 下载并安装比特浏览器")
    print("   安装后请启动比特浏览器客户端")
    return False

def test_system():
    """测试系统功能"""
    print("\n🧪 测试系统功能...")
    
    try:
        # 测试导入模块
        print("  测试模块导入...")
        import selenium
        import requests
        import faker
        print("  ✅ 模块导入成功")
        
        # 测试比特浏览器API
        print("  测试比特浏览器API...")
        from bit_browser_api import BitBrowserAPI
        
        api = BitBrowserAPI("TestProject")
        if api.bit_port:
            print("  ✅ 比特浏览器API连接成功")
        else:
            print("  ❌ 比特浏览器API连接失败")
            return False
        
        # 测试管理器
        print("  测试浏览器管理器...")
        from fingerprint_browser_manager import FingerprintBrowserManager
        
        manager = FingerprintBrowserManager("TestProject")
        print("  ✅ 浏览器管理器初始化成功")
        
        print("✅ 系统功能测试通过")
        return True
        
    except ImportError as e:
        print(f"  ❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 系统测试失败: {e}")
        return False

def create_example_config():
    """创建示例配置文件"""
    print("\n📝 创建示例配置文件...")
    
    example_config = {
        "project_name": "MyProject",
        "default_group": "default",
        "default_fingerprint": {
            "resolution": "1920x1080",
            "timeZone": "Asia/Shanghai",
            "languages": "zh-CN,zh",
            "webGL": "0",
            "canvas": "0",
            "hardwareConcurrency": "8",
            "deviceMemory": "16"
        },
        "proxy_examples": [
            {
                "name": "HTTP代理示例",
                "type": "http",
                "host": "127.0.0.1",
                "port": "8080",
                "username": "",
                "password": ""
            },
            {
                "name": "SOCKS5代理示例",
                "type": "socks5",
                "host": "127.0.0.1",
                "port": "1080",
                "username": "",
                "password": ""
            }
        ]
    }
    
    config_file = "fingerprint_browser_config.json"
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(example_config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 示例配置文件已创建: {config_file}")
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 使用指南:")
    print("-" * 50)
    print("1. 启动交互式界面:")
    print("   python browser_cli.py")
    print()
    print("2. 运行示例代码:")
    print("   python fingerprint_browser_examples.py")
    print()
    print("3. 查看详细文档:")
    print("   README_fingerprint_browser.md")
    print()
    print("4. 基础使用代码:")
    print("   from fingerprint_browser_manager import FingerprintBrowserManager")
    print("   manager = FingerprintBrowserManager('MyProject')")
    print("   # 创建和管理浏览器...")
    print()

def main():
    """主安装流程"""
    print_banner()
    
    success = True
    
    # 检查Python版本
    if not check_python_version():
        success = False
    
    # 安装Python包
    if success and not install_python_packages():
        success = False
    
    # 检查Chrome浏览器
    if not check_chrome_browser():
        print("⚠️ Chrome浏览器未安装，可能影响功能使用")
    
    # 检查比特浏览器
    if not check_bit_browser():
        print("⚠️ 比特浏览器未安装或未启动，这是必需的组件")
        success = False
    
    # 测试系统功能
    if success and not test_system():
        success = False
    
    # 创建示例配置
    create_example_config()
    
    # 显示结果
    print("\n" + "=" * 70)
    if success:
        print("🎉 安装完成！系统已准备就绪")
        show_usage_guide()
    else:
        print("❌ 安装过程中遇到问题，请检查上述错误信息")
        print("\n🔧 解决方案:")
        print("1. 确保已安装比特浏览器并启动")
        print("2. 确保网络连接正常")
        print("3. 以管理员权限运行此脚本")
        print("4. 检查防火墙和杀毒软件设置")
    
    print("=" * 70)
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
