#!/usr/bin/env python3
"""
Augment 账号池管理系统
支持使用 Access Token 直接切换本地 Augment 账号登录
"""

import json
import os
import shutil
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AugmentAccountPool:
    """Augment 账号池管理器"""
    
    def __init__(self, pool_file: str = "augment_accounts.json"):
        self.pool_file = pool_file
        self.accounts = self.load_accounts()
        self.vscode_user_dir = Path(os.path.expandvars(r"%APPDATA%\Code\User"))
        self.augment_global_storage = self.vscode_user_dir / "globalStorage" / "augment.vscode-augment"
        
    def load_accounts(self) -> Dict:
        """加载账号池配置"""
        if os.path.exists(self.pool_file):
            try:
                with open(self.pool_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载账号池失败: {e}")
                return {"accounts": [], "current_account": None}
        return {"accounts": [], "current_account": None}
    
    def save_accounts(self):
        """保存账号池配置"""
        try:
            with open(self.pool_file, 'w', encoding='utf-8') as f:
                json.dump(self.accounts, f, indent=2, ensure_ascii=False)
            logger.info("账号池配置已保存")
        except Exception as e:
            logger.error(f"保存账号池失败: {e}")
    
    def add_account(self, name: str, access_token: str, tenant_url: str, 
                   email: str = "", description: str = ""):
        """添加新账号到池中"""
        account = {
            "name": name,
            "access_token": access_token,
            "tenant_url": tenant_url,
            "email": email,
            "description": description,
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "last_used": None
        }
        
        # 检查是否已存在同名账号
        for i, acc in enumerate(self.accounts["accounts"]):
            if acc["name"] == name:
                self.accounts["accounts"][i] = account
                logger.info(f"更新账号: {name}")
                self.save_accounts()
                return
        
        self.accounts["accounts"].append(account)
        logger.info(f"添加新账号: {name}")
        self.save_accounts()
    
    def list_accounts(self) -> List[Dict]:
        """列出所有账号"""
        return self.accounts["accounts"]
    
    def get_account(self, name: str) -> Optional[Dict]:
        """获取指定账号信息"""
        for account in self.accounts["accounts"]:
            if account["name"] == name:
                return account
        return None
    
    def remove_account(self, name: str) -> bool:
        """删除账号"""
        for i, account in enumerate(self.accounts["accounts"]):
            if account["name"] == name:
                del self.accounts["accounts"][i]
                if self.accounts["current_account"] == name:
                    self.accounts["current_account"] = None
                logger.info(f"删除账号: {name}")
                self.save_accounts()
                return True
        return False
    
    def backup_current_config(self) -> bool:
        """备份当前 Augment 配置"""
        try:
            if not self.augment_global_storage.exists():
                logger.warning("未找到 Augment 配置目录")
                return False
            
            backup_dir = Path("augment_config_backup")
            backup_dir.mkdir(exist_ok=True)
            
            current_backup = backup_dir / f"current_{int(time.time())}"
            shutil.copytree(self.augment_global_storage, current_backup)
            
            logger.info(f"配置已备份到: {current_backup}")
            return True
        except Exception as e:
            logger.error(f"备份配置失败: {e}")
            return False
    
    def create_account_config(self, account: Dict) -> bool:
        """为指定账号创建配置文件"""
        try:
            # 创建账号专用配置目录
            account_config_dir = Path("augment_configs") / account["name"]
            account_config_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建认证配置
            auth_config = {
                "access_token": account["access_token"],
                "tenant_url": account["tenant_url"],
                "email": account["email"],
                "last_login": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            auth_file = account_config_dir / "auth.json"
            with open(auth_file, 'w', encoding='utf-8') as f:
                json.dump(auth_config, f, indent=2)
            
            # 创建 VS Code 设置覆盖
            vscode_settings = {
                "augment.apiUrl": account["tenant_url"],
                "augment.accessToken": account["access_token"]
            }
            
            settings_file = account_config_dir / "vscode_settings.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(vscode_settings, f, indent=2)
            
            logger.info(f"为账号 {account['name']} 创建配置文件")
            return True
            
        except Exception as e:
            logger.error(f"创建账号配置失败: {e}")
            return False
    
    def switch_account(self, name: str) -> bool:
        """切换到指定账号"""
        account = self.get_account(name)
        if not account:
            logger.error(f"账号不存在: {name}")
            return False
        
        try:
            # 1. 备份当前配置
            self.backup_current_config()
            
            # 2. 设置环境变量
            os.environ["AUGMENT_ACCESS_TOKEN"] = account["access_token"]
            os.environ["AUGMENT_TENANT_URL"] = account["tenant_url"]
            
            # 3. 创建账号配置
            self.create_account_config(account)
            
            # 4. 重启 VS Code 以应用新配置
            if self.restart_vscode():
                # 5. 更新当前账号和最后使用时间
                account["last_used"] = time.strftime("%Y-%m-%d %H:%M:%S")
                self.accounts["current_account"] = name
                self.save_accounts()
                
                logger.info(f"成功切换到账号: {name}")
                return True
            else:
                logger.warning("VS Code 重启失败，请手动重启")
                return False
                
        except Exception as e:
            logger.error(f"切换账号失败: {e}")
            return False
    
    def restart_vscode(self) -> bool:
        """重启 VS Code"""
        try:
            # 关闭所有 VS Code 进程
            subprocess.run(["taskkill", "/f", "/im", "Code.exe"], 
                         capture_output=True, check=False)
            time.sleep(2)
            
            # 重新启动 VS Code
            subprocess.Popen(["code", "."], cwd=os.getcwd())
            time.sleep(3)
            
            return True
        except Exception as e:
            logger.error(f"重启 VS Code 失败: {e}")
            return False
    
    def get_current_account(self) -> Optional[str]:
        """获取当前使用的账号"""
        return self.accounts.get("current_account")
    
    def export_accounts(self, export_file: str) -> bool:
        """导出账号池到文件"""
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(self.accounts, f, indent=2, ensure_ascii=False)
            logger.info(f"账号池已导出到: {export_file}")
            return True
        except Exception as e:
            logger.error(f"导出失败: {e}")
            return False
    
    def import_accounts(self, import_file: str) -> bool:
        """从文件导入账号池"""
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                imported_data = json.load(f)
            
            # 合并账号，避免重复
            existing_names = {acc["name"] for acc in self.accounts["accounts"]}
            new_accounts = [acc for acc in imported_data.get("accounts", []) 
                          if acc["name"] not in existing_names]
            
            self.accounts["accounts"].extend(new_accounts)
            self.save_accounts()
            
            logger.info(f"成功导入 {len(new_accounts)} 个新账号")
            return True
        except Exception as e:
            logger.error(f"导入失败: {e}")
            return False


def main():
    """主函数 - 命令行界面"""
    pool = AugmentAccountPool()
    
    while True:
        print("\n" + "="*50)
        print("🔧 Augment 账号池管理系统")
        print("="*50)
        print("1. 添加账号")
        print("2. 列出所有账号")
        print("3. 切换账号")
        print("4. 删除账号")
        print("5. 查看当前账号")
        print("6. 导出账号池")
        print("7. 导入账号池")
        print("0. 退出")
        print("-"*50)
        
        choice = input("请选择操作 (0-7): ").strip()
        
        if choice == "1":
            print("\n📝 添加新账号")
            name = input("账号名称: ").strip()
            access_token = input("Access Token: ").strip()
            tenant_url = input("Tenant URL: ").strip()
            email = input("邮箱 (可选): ").strip()
            description = input("描述 (可选): ").strip()
            
            if name and access_token and tenant_url:
                pool.add_account(name, access_token, tenant_url, email, description)
                print(f"✅ 账号 '{name}' 添加成功")
            else:
                print("❌ 必填字段不能为空")
        
        elif choice == "2":
            print("\n📋 账号列表")
            accounts = pool.list_accounts()
            if not accounts:
                print("暂无账号")
            else:
                current = pool.get_current_account()
                for i, acc in enumerate(accounts, 1):
                    status = " [当前]" if acc["name"] == current else ""
                    print(f"{i}. {acc['name']}{status}")
                    print(f"   邮箱: {acc.get('email', 'N/A')}")
                    print(f"   描述: {acc.get('description', 'N/A')}")
                    print(f"   最后使用: {acc.get('last_used', '从未使用')}")
                    print()
        
        elif choice == "3":
            print("\n🔄 切换账号")
            accounts = pool.list_accounts()
            if not accounts:
                print("暂无可用账号")
                continue
            
            print("可用账号:")
            for i, acc in enumerate(accounts, 1):
                print(f"{i}. {acc['name']}")
            
            try:
                idx = int(input("选择账号编号: ")) - 1
                if 0 <= idx < len(accounts):
                    account_name = accounts[idx]["name"]
                    print(f"正在切换到账号: {account_name}")
                    if pool.switch_account(account_name):
                        print("✅ 切换成功")
                    else:
                        print("❌ 切换失败")
                else:
                    print("❌ 无效的账号编号")
            except ValueError:
                print("❌ 请输入有效数字")
        
        elif choice == "4":
            print("\n🗑️ 删除账号")
            accounts = pool.list_accounts()
            if not accounts:
                print("暂无账号可删除")
                continue
            
            print("现有账号:")
            for i, acc in enumerate(accounts, 1):
                print(f"{i}. {acc['name']}")
            
            try:
                idx = int(input("选择要删除的账号编号: ")) - 1
                if 0 <= idx < len(accounts):
                    account_name = accounts[idx]["name"]
                    confirm = input(f"确认删除账号 '{account_name}'? (y/N): ").strip().lower()
                    if confirm == 'y':
                        if pool.remove_account(account_name):
                            print("✅ 删除成功")
                        else:
                            print("❌ 删除失败")
                    else:
                        print("操作已取消")
                else:
                    print("❌ 无效的账号编号")
            except ValueError:
                print("❌ 请输入有效数字")
        
        elif choice == "5":
            print("\n👤 当前账号")
            current = pool.get_current_account()
            if current:
                account = pool.get_account(current)
                print(f"当前使用账号: {current}")
                print(f"邮箱: {account.get('email', 'N/A')}")
                print(f"Tenant URL: {account['tenant_url']}")
                print(f"最后使用: {account.get('last_used', '从未使用')}")
            else:
                print("当前未设置账号")
        
        elif choice == "6":
            print("\n📤 导出账号池")
            export_file = input("导出文件名 (默认: accounts_export.json): ").strip()
            if not export_file:
                export_file = "accounts_export.json"
            
            if pool.export_accounts(export_file):
                print("✅ 导出成功")
            else:
                print("❌ 导出失败")
        
        elif choice == "7":
            print("\n📥 导入账号池")
            import_file = input("导入文件名: ").strip()
            if import_file and os.path.exists(import_file):
                if pool.import_accounts(import_file):
                    print("✅ 导入成功")
                else:
                    print("❌ 导入失败")
            else:
                print("❌ 文件不存在")
        
        elif choice == "0":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重试")


if __name__ == "__main__":
    main()
