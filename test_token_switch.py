#!/usr/bin/env python3
"""
测试 Augment Token 切换功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from augment_token_switcher import AugmentTokenSwitcher

def test_token_switch():
    """测试 Token 切换功能"""
    print("🧪 测试 Augment Token 切换功能")
    print("="*40)
    
    # 你的 Token 信息
    access_token = "aaabd8f0d4ab1233d05139a2f483f5a3ad33d3dc63586f5624b6aae7b8c0c77f"
    tenant_url = "https://d5.api.augmentcode.com/"
    
    # 创建切换器
    switcher = AugmentTokenSwitcher()
    
    print("1. 查看当前状态...")
    current_info = switcher.get_current_token_info()
    print(f"   当前 Token: {current_info['access_token'][:20]}..." if len(current_info['access_token']) > 20 else current_info['access_token'])
    print(f"   当前 URL: {current_info['tenant_url']}")
    
    print("\n2. 创建测试配置文件...")
    switcher.create_token_profile(
        name="test_account",
        access_token=access_token,
        tenant_url=tenant_url,
        email="<EMAIL>",
        description="测试账号配置"
    )
    print("   ✅ 配置文件已创建")
    
    print("\n3. 列出所有配置...")
    profiles = switcher.list_token_profiles()
    for i, profile in enumerate(profiles, 1):
        print(f"   {i}. {profile['name']} - {profile.get('email', 'N/A')}")
    
    print("\n4. 测试 Token 设置（不重启 VS Code）...")
    try:
        switcher.set_augment_token(access_token, tenant_url)
        print("   ✅ Token 设置成功")
    except Exception as e:
        print(f"   ❌ Token 设置失败: {e}")
    
    print("\n5. 验证设置结果...")
    new_info = switcher.get_current_token_info()
    print(f"   新 Token: {new_info['access_token'][:20]}..." if len(new_info['access_token']) > 20 else new_info['access_token'])
    print(f"   新 URL: {new_info['tenant_url']}")
    
    # 检查是否设置成功
    if access_token in new_info['access_token'] and tenant_url in new_info['tenant_url']:
        print("   ✅ Token 切换成功！")
        
        print("\n6. 询问是否重启 VS Code...")
        restart = input("   是否重启 VS Code 以应用新配置? (y/N): ").strip().lower()
        if restart == 'y':
            print("   正在重启 VS Code...")
            switcher.kill_vscode_processes()
            switcher.start_vscode()
            print("   ✅ VS Code 已重启")
        else:
            print("   跳过重启，请手动重启 VS Code 以应用新配置")
    else:
        print("   ❌ Token 切换可能失败，请检查配置")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    test_token_switch()
