#!/usr/bin/env python3
"""
网站注册自动化脚本
注意：请确保遵守网站服务条款和相关法律法规
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging
import config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RegistrationBot:
    def __init__(self, headless=False, wait_timeout=10):
        """
        初始化注册机器人
        
        Args:
            headless (bool): 是否使用无头模式
            wait_timeout (int): 等待超时时间（秒）
        """
        self.wait_timeout = wait_timeout
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置WebDriver - 支持Chrome和Edge"""
        # 首先尝试Chrome
        if self._try_chrome(headless):
            return

        # 如果Chrome失败，尝试Edge
        if self._try_edge(headless):
            return

        # 如果都失败了，抛出异常
        raise Exception("无法初始化任何WebDriver（Chrome和Edge都失败了）")

    def _try_chrome(self, headless=False):
        """尝试设置Chrome WebDriver"""
        try:
            logger.info("尝试初始化Chrome WebDriver...")
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")

            # 指定Chrome二进制文件路径
            chrome_binary_path = getattr(config, 'CHROME_BINARY_PATH', None)
            if chrome_binary_path and os.path.exists(chrome_binary_path):
                chrome_options.binary_location = chrome_binary_path
                logger.info(f"使用指定的Chrome路径: {chrome_binary_path}")
            else:
                # 尝试常见的Chrome安装路径
                possible_paths = [
                    r"E:\Google\Chrome\Application\chrome.exe",  # 用户的路径
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        chrome_options.binary_location = path
                        logger.info(f"找到Chrome浏览器: {path}")
                        break
                else:
                    logger.warning("未找到Chrome浏览器，尝试使用系统默认路径")

            # 添加一些常用选项
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 设置用户代理
            user_agent = random.choice(config.USER_AGENTS)
            chrome_options.add_argument(f"--user-agent={user_agent}")

            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, self.wait_timeout)

            logger.info("Chrome WebDriver 初始化成功")
            return True

        except Exception as e:
            logger.warning(f"Chrome WebDriver 初始化失败: {e}")
            return False

    def _try_edge(self, headless=False):
        """尝试设置Edge WebDriver"""
        try:
            logger.info("尝试初始化Edge WebDriver...")
            from selenium.webdriver.edge.options import Options as EdgeOptions
            from selenium.webdriver.edge.service import Service as EdgeService
            from webdriver_manager.microsoft import EdgeChromiumDriverManager

            edge_options = EdgeOptions()
            if headless:
                edge_options.add_argument("--headless")

            # 添加一些常用选项
            edge_options.add_argument("--no-sandbox")
            edge_options.add_argument("--disable-dev-shm-usage")
            edge_options.add_argument("--disable-blink-features=AutomationControlled")
            edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            edge_options.add_experimental_option('useAutomationExtension', False)

            # 设置用户代理
            user_agent = random.choice(config.USER_AGENTS)
            edge_options.add_argument(f"--user-agent={user_agent}")

            # 使用webdriver-manager自动管理EdgeDriver
            service = EdgeService(EdgeChromiumDriverManager().install())
            self.driver = webdriver.Edge(service=service, options=edge_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, self.wait_timeout)

            logger.info("Edge WebDriver 初始化成功")
            return True

        except Exception as e:
            logger.warning(f"Edge WebDriver 初始化失败: {e}")
            return False
    
    def navigate_to_registration(self, url):
        """导航到注册页面"""
        try:
            logger.info(f"正在访问注册页面: {url}")
            self.driver.get(url)
            time.sleep(random.uniform(2, 4))  # 随机等待
            return True
        except Exception as e:
            logger.error(f"无法访问注册页面: {e}")
            return False
    
    def fill_registration_form(self, user_data):
        """
        填写注册表单
        
        Args:
            user_data (dict): 用户数据字典，包含所需的注册信息
        """
        try:
            # 这里需要根据实际网站的表单结构进行调整
            # 以下是常见的表单字段示例
            
            # 等待表单加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "form")))
            
            # 填写邮箱
            if 'email' in user_data:
                email_field = self.find_element_by_multiple_selectors([
                    "input[type='email']",
                    "input[name='email']",
                    "input[id='email']",
                    "#email"
                ])
                if email_field:
                    self.type_slowly(email_field, user_data['email'])
                    logger.info("邮箱填写完成")
            
            # 填写密码
            if 'password' in user_data:
                password_field = self.find_element_by_multiple_selectors([
                    "input[type='password']",
                    "input[name='password']",
                    "input[id='password']",
                    "#password"
                ])
                if password_field:
                    self.type_slowly(password_field, user_data['password'])
                    logger.info("密码填写完成")
            
            # 填写确认密码
            if 'confirm_password' in user_data:
                confirm_field = self.find_element_by_multiple_selectors([
                    "input[name='confirm_password']",
                    "input[name='password_confirmation']",
                    "input[id='confirm_password']",
                    "#confirm_password"
                ])
                if confirm_field:
                    self.type_slowly(confirm_field, user_data['confirm_password'])
                    logger.info("确认密码填写完成")
            
            # 填写用户名（如果需要）
            if 'username' in user_data:
                username_field = self.find_element_by_multiple_selectors([
                    "input[name='username']",
                    "input[id='username']",
                    "#username"
                ])
                if username_field:
                    self.type_slowly(username_field, user_data['username'])
                    logger.info("用户名填写完成")
            
            return True
            
        except Exception as e:
            logger.error(f"填写表单时出错: {e}")
            return False
    
    def find_element_by_multiple_selectors(self, selectors):
        """尝试多个选择器来查找元素"""
        for selector in selectors:
            try:
                if selector.startswith('#') or selector.startswith('.') or '[' in selector:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                else:
                    element = self.driver.find_element(By.NAME, selector)
                return element
            except NoSuchElementException:
                continue
        return None
    
    def type_slowly(self, element, text, delay_range=(0.05, 0.15)):
        """模拟人类打字速度"""
        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(*delay_range))
    
    def handle_captcha(self):
        """处理验证码（需要人工干预或第三方服务）"""
        logger.info("检测到验证码，请手动完成...")
        input("完成验证码后按回车继续...")
    
    def submit_form(self):
        """提交注册表单"""
        try:
            # 查找提交按钮
            submit_button = self.find_element_by_multiple_selectors([
                "button[type='submit']",
                "input[type='submit']",
                "button[class*='submit']",
                "button[class*='register']",
                ".submit-btn",
                ".register-btn"
            ])
            
            if submit_button:
                # 滚动到按钮位置
                self.driver.execute_script("arguments[0].scrollIntoView();", submit_button)
                time.sleep(1)
                
                # 点击提交
                submit_button.click()
                logger.info("表单提交成功")
                return True
            else:
                logger.error("未找到提交按钮")
                return False
                
        except Exception as e:
            logger.error(f"提交表单时出错: {e}")
            return False
    
    def wait_for_success(self):
        """等待注册成功的确认"""
        try:
            # 等待成功页面或成功消息
            success_indicators = [
                "//div[contains(text(), 'success')]",
                "//div[contains(text(), '成功')]",
                "//div[contains(text(), 'welcome')]",
                "//div[contains(text(), '欢迎')]"
            ]
            
            for indicator in success_indicators:
                try:
                    self.wait.until(EC.presence_of_element_located((By.XPATH, indicator)))
                    logger.info("注册成功！")
                    return True
                except TimeoutException:
                    continue
            
            logger.warning("未检测到明确的成功指示")
            return False
            
        except Exception as e:
            logger.error(f"等待成功确认时出错: {e}")
            return False
    
    def register(self, url, user_data):
        """
        执行完整的注册流程
        
        Args:
            url (str): 注册页面URL
            user_data (dict): 用户注册数据
        """
        try:
            # 1. 导航到注册页面
            if not self.navigate_to_registration(url):
                return False
            
            # 2. 填写注册表单
            if not self.fill_registration_form(user_data):
                return False
            
            # 3. 处理可能的验证码
            # 这里可以添加验证码检测逻辑
            
            # 4. 提交表单
            if not self.submit_form():
                return False
            
            # 5. 等待成功确认
            return self.wait_for_success()
            
        except Exception as e:
            logger.error(f"注册过程中出错: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数示例"""
    # 示例用户数据
    user_data = {
        'email': '<EMAIL>',
        'password': 'SecurePassword123!',
        'confirm_password': 'SecurePassword123!',
        'username': 'example_user'
    }
    
    # 注册页面URL（需要替换为实际URL）
    registration_url = "https://app.augmentcode.com/register"  # 示例URL
    
    bot = RegistrationBot(headless=False)  # 设置为True使用无头模式
    
    try:
        success = bot.register(registration_url, user_data)
        if success:
            print("注册成功！")
        else:
            print("注册失败，请检查日志")
    finally:
        bot.close()

if __name__ == "__main__":
    main()
