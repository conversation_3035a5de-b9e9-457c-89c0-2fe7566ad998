#!/usr/bin/env python3
"""
安装和设置脚本
"""

import os
import subprocess
import sys
import shutil

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    else:
        print(f"✅ Python版本: {sys.version}")

def check_chrome():
    """检查Chrome浏览器是否安装"""
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ 找到Chrome浏览器: {path}")
            return True
    
    print("⚠️  未找到Chrome浏览器，请确保已安装Chrome")
    return False

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖包安装失败")
        return False

def setup_env_file():
    """设置环境变量文件"""
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("✅ 已创建 .env 文件")
            print("请编辑 .env 文件并填入您的注册信息")
        else:
            print("❌ 未找到 .env.example 文件")
            return False
    else:
        print("✅ .env 文件已存在")
    return True

def test_installation():
    """测试安装"""
    print("正在测试安装...")
    try:
        import selenium
        from webdriver_manager.chrome import ChromeDriverManager
        print("✅ Selenium导入成功")
        
        # 测试ChromeDriver下载
        ChromeDriverManager().install()
        print("✅ ChromeDriver安装成功")
        
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主安装流程"""
    print("=== 网站注册自动化脚本安装程序 ===\n")
    
    # 检查Python版本
    check_python_version()
    
    # 检查Chrome浏览器
    check_chrome()
    
    # 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 设置环境文件
    if not setup_env_file():
        sys.exit(1)
    
    # 测试安装
    if not test_installation():
        sys.exit(1)
    
    print("\n🎉 安装完成！")
    print("\n下一步:")
    print("1. 编辑 .env 文件，填入您的注册信息")
    print("2. 修改 config.py 中的网站URL和选择器")
    print("3. 运行: python example_usage.py")
    print("\n⚠️  请确保遵守网站服务条款和相关法律法规")

if __name__ == "__main__":
    main()
