#!/usr/bin/env python3
"""
简单的浏览器测试脚本
"""

def test_chrome():
    """测试Chrome"""
    print("测试Chrome...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.binary_location = r"E:\Google\Chrome\Application\chrome.exe"
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.get("https://www.google.com")
        print(f"✅ Chrome测试成功！标题: {driver.title}")
        driver.quit()
        return True
    except Exception as e:
        print(f"❌ Chrome测试失败: {e}")
        return False

def test_edge():
    """测试Edge"""
    print("测试Edge...")
    try:
        from selenium import webdriver
        from selenium.webdriver.edge.options import Options
        from selenium.webdriver.edge.service import Service
        from webdriver_manager.microsoft import EdgeChromiumDriverManager
        
        edge_options = Options()
        edge_options.add_argument("--headless")
        
        service = Service(EdgeChromiumDriverManager().install())
        driver = webdriver.Edge(service=service, options=edge_options)
        driver.get("https://www.google.com")
        print(f"✅ Edge测试成功！标题: {driver.title}")
        driver.quit()
        return True
    except Exception as e:
        print(f"❌ Edge测试失败: {e}")
        return False

def main():
    print("=== 浏览器兼容性测试 ===\n")
    
    chrome_ok = test_chrome()
    print()
    edge_ok = test_edge()
    
    print("\n=== 测试结果 ===")
    if chrome_ok:
        print("✅ Chrome可用")
    if edge_ok:
        print("✅ Edge可用")
    
    if not chrome_ok and not edge_ok:
        print("❌ 没有可用的浏览器")
    elif chrome_ok:
        print("\n推荐使用Chrome版本的脚本")
    elif edge_ok:
        print("\n推荐使用Edge版本的脚本")

if __name__ == "__main__":
    main()
