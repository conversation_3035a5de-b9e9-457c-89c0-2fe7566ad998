#!/usr/bin/env python3
"""
注册脚本使用示例
"""

from registration_automation import RegistrationBot
import config
import logging

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config.LOG_FILE),
        logging.StreamHandler()
    ]
)

def register_single_account():
    """注册单个账户的示例"""
    
    # 用户数据
    user_data = {
        'email': config.EMAIL or '<EMAIL>',
        'password': config.PASSWORD or 'SecurePassword123!',
        'confirm_password': config.PASSWORD or 'SecurePassword123!',
        'username': config.USERNAME or 'test_user'
    }
    
    # 创建注册机器人实例
    bot = RegistrationBot(
        headless=config.HEADLESS_MODE,
        wait_timeout=config.WAIT_TIMEOUT
    )
    
    try:
        print("开始注册流程...")
        success = bot.register(config.REGISTRATION_URL, user_data)
        
        if success:
            print("✅ 注册成功！")
        else:
            print("❌ 注册失败，请检查日志文件")
            
    except Exception as e:
        print(f"❌ 注册过程中出现错误: {e}")
        
    finally:
        bot.close()

def register_multiple_accounts():
    """批量注册多个账户的示例"""
    
    # 多个用户数据
    users_data = [
        {
            'email': '<EMAIL>',
            'password': 'SecurePassword123!',
            'confirm_password': 'SecurePassword123!',
            'username': 'user1'
        },
        {
            'email': '<EMAIL>',
            'password': 'SecurePassword456!',
            'confirm_password': 'SecurePassword456!',
            'username': 'user2'
        }
        # 添加更多用户...
    ]
    
    success_count = 0
    total_count = len(users_data)
    
    for i, user_data in enumerate(users_data, 1):
        print(f"\n正在注册第 {i}/{total_count} 个账户...")
        print(f"邮箱: {user_data['email']}")
        
        bot = RegistrationBot(
            headless=config.HEADLESS_MODE,
            wait_timeout=config.WAIT_TIMEOUT
        )
        
        try:
            success = bot.register(config.REGISTRATION_URL, user_data)
            
            if success:
                success_count += 1
                print(f"✅ 账户 {user_data['email']} 注册成功")
            else:
                print(f"❌ 账户 {user_data['email']} 注册失败")
                
        except Exception as e:
            print(f"❌ 注册账户 {user_data['email']} 时出错: {e}")
            
        finally:
            bot.close()
        
        # 在注册之间添加延迟
        if i < total_count:
            import time
            import random
            delay = random.uniform(config.MIN_DELAY, config.MAX_DELAY)
            print(f"等待 {delay:.1f} 秒后继续...")
            time.sleep(delay)
    
    print(f"\n批量注册完成: {success_count}/{total_count} 个账户注册成功")

def interactive_registration():
    """交互式注册"""
    print("=== 交互式注册 ===")
    
    # 获取用户输入
    email = input("请输入邮箱: ").strip()
    password = input("请输入密码: ").strip()
    confirm_password = input("请确认密码: ").strip()
    username = input("请输入用户名 (可选): ").strip()
    
    if password != confirm_password:
        print("❌ 密码不匹配！")
        return
    
    user_data = {
        'email': email,
        'password': password,
        'confirm_password': confirm_password
    }
    
    if username:
        user_data['username'] = username
    
    # 询问是否使用无头模式
    headless_choice = input("是否使用无头模式？(y/N): ").strip().lower()
    headless = headless_choice in ['y', 'yes', '是']
    
    bot = RegistrationBot(headless=headless, wait_timeout=config.WAIT_TIMEOUT)
    
    try:
        print("\n开始注册...")
        success = bot.register(config.REGISTRATION_URL, user_data)
        
        if success:
            print("✅ 注册成功！")
        else:
            print("❌ 注册失败")
            
    except Exception as e:
        print(f"❌ 注册过程中出错: {e}")
        
    finally:
        bot.close()

def main():
    """主菜单"""
    print("=== 网站注册自动化工具 ===")
    print("1. 单个账户注册")
    print("2. 批量账户注册")
    print("3. 交互式注册")
    print("0. 退出")
    
    while True:
        choice = input("\n请选择操作 (0-3): ").strip()
        
        if choice == '1':
            register_single_account()
            break
        elif choice == '2':
            register_multiple_accounts()
            break
        elif choice == '3':
            interactive_registration()
            break
        elif choice == '0':
            print("再见！")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
