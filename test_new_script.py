#!/usr/bin/env python3
"""
测试新脚本功能
"""

print("=== 测试新脚本功能 ===")

try:
    from user_config import USER_INFO, BROWSER_CONFIG, print_config_status
    print("✅ 配置文件导入成功")
    
    print(f"邮箱配置: {USER_INFO['email']}")
    print(f"Chrome路径: {BROWSER_CONFIG['chrome_path']}")
    
    # 检查配置状态
    print("\n=== 配置状态检查 ===")
    config_ok = print_config_status()
    
    if not config_ok:
        print("\n⚠️  请修改 user_config.py 中的配置信息")
    else:
        print("\n✅ 配置验证通过，可以运行自动注册脚本")
        
except ImportError as e:
    print(f"❌ 导入配置文件失败: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")

print("\n=== 可用脚本列表 ===")
import os
scripts = [
    "smart_auto_register.py",
    "auto_register.py", 
    "subscription_automation.py",
    "fixed_registration.py"
]

for script in scripts:
    if os.path.exists(script):
        print(f"✅ {script}")
    else:
        print(f"❌ {script} (不存在)")

print("\n=== 推荐使用 ===")
print("1. 首先修改 user_config.py 中的用户信息")
print("2. 运行: python smart_auto_register.py")
print("3. 选择需要的操作（注册/登录/访问订阅页面）")
