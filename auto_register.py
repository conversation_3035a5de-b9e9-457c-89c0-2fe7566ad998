#!/usr/bin/env python3
"""
Augment Code 自动注册脚本
支持自动填写注册信息和登录信息
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Chrome路径
CHROME_PATH = r"E:\Google\Chrome\Application\chrome.exe"

# 用户配置 - 请修改这些信息
USER_CONFIG = {
    "email": "<EMAIL>",  # 请修改为您的邮箱
    "password": "YourSecurePassword123!",  # 请修改为您的密码
    "username": "your_username",  # 请修改为您的用户名（如果需要）
    "confirm_password": None  # 如果为None，将使用相同的密码
}

# 网站URL配置
URLS = {
    "register": "https://app.augmentcode.com/register",
    "login": "https://app.augmentcode.com/login",
    "subscription": "https://app.augmentcode.com/account/subscription"
}

class AutoRegisterBot:
    def __init__(self, headless=False):
        """初始化自动注册机器人"""
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """设置Chrome WebDriver - 无痕模式"""
        try:
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")
            
            # 直接指定Chrome路径
            chrome_options.binary_location = CHROME_PATH
            
            # 启用无痕模式
            chrome_options.add_argument("--incognito")
            chrome_options.add_argument("--new-window")
            
            # 添加其他选项
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 禁用各种弹窗
            chrome_options.add_experimental_option("prefs", {
                "credentials_enable_service": False,
                "profile.password_manager_enabled": False,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0
            })
            
            # 用户代理
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # 获取ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 15)
            
            # 最大化窗口
            self.driver.maximize_window()
            
            logger.info("Chrome无痕模式 WebDriver 初始化成功")
            
        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def navigate_to_page(self, url):
        """导航到指定页面"""
        try:
            logger.info(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(3)
            
            page_title = self.driver.title
            current_url = self.driver.current_url
            logger.info(f"页面标题: {page_title}")
            logger.info(f"当前URL: {current_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"无法访问页面: {e}")
            return False
    
    def type_slowly(self, element, text, delay_range=(0.05, 0.15)):
        """模拟人类打字速度"""
        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(*delay_range))
    
    def fill_field_by_selectors(self, selectors, value, field_name):
        """通过多个选择器尝试填写字段"""
        for selector in selectors:
            try:
                field = self.driver.find_element(By.CSS_SELECTOR, selector)
                if field.is_displayed():
                    self.type_slowly(field, value)
                    logger.info(f"{field_name}填写成功: {selector}")
                    return True
            except:
                continue
        
        logger.warning(f"未找到{field_name}输入框")
        return False
    
    def auto_register(self, email, password, username=None, confirm_password=None):
        """自动注册流程"""
        try:
            logger.info("开始自动注册流程...")
            
            # 1. 访问注册页面
            if not self.navigate_to_page(URLS["register"]):
                return False
            
            # 2. 检查是否已经在注册页面
            if "register" not in self.driver.current_url.lower() and "sign" not in self.driver.current_url.lower():
                logger.warning("可能不在注册页面，尝试查找注册链接...")
                self.find_and_click_register_link()
            
            # 3. 填写注册表单
            if not confirm_password:
                confirm_password = password
            
            # 填写邮箱
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[id='email']",
                "input[placeholder*='email' i]"
            ]
            
            if not self.fill_field_by_selectors(email_selectors, email, "邮箱"):
                return False
            
            # 填写用户名（如果需要）
            if username:
                username_selectors = [
                    "input[name='username']",
                    "input[id='username']",
                    "input[placeholder*='username' i]",
                    "input[name='name']",
                    "input[id='name']"
                ]
                self.fill_field_by_selectors(username_selectors, username, "用户名")
            
            # 填写密码
            password_selectors = [
                "input[type='password'][name*='password']:not([name*='confirm'])",
                "input[type='password'][id*='password']:not([id*='confirm'])",
                "input[type='password']:first-of-type"
            ]
            
            if not self.fill_field_by_selectors(password_selectors, password, "密码"):
                return False
            
            # 填写确认密码
            confirm_password_selectors = [
                "input[name*='confirm']",
                "input[id*='confirm']",
                "input[placeholder*='confirm' i]",
                "input[type='password']:last-of-type"
            ]
            
            self.fill_field_by_selectors(confirm_password_selectors, confirm_password, "确认密码")
            
            # 4. 点击注册按钮
            if self.click_register_button():
                logger.info("注册表单提交成功")
                time.sleep(5)  # 等待处理
                return True
            else:
                logger.error("注册按钮点击失败")
                return False
            
        except Exception as e:
            logger.error(f"自动注册失败: {e}")
            return False
    
    def auto_login(self, email, password):
        """自动登录流程"""
        try:
            logger.info("开始自动登录流程...")
            
            # 1. 访问登录页面
            if not self.navigate_to_page(URLS["login"]):
                return False
            
            # 2. 填写登录表单
            # 填写邮箱
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "input[id='email']",
                "input[name='username']",
                "input[id='username']"
            ]
            
            if not self.fill_field_by_selectors(email_selectors, email, "邮箱"):
                return False
            
            # 填写密码
            password_selectors = [
                "input[type='password']",
                "input[name='password']",
                "input[id='password']"
            ]
            
            if not self.fill_field_by_selectors(password_selectors, password, "密码"):
                return False
            
            # 3. 点击登录按钮
            if self.click_login_button():
                logger.info("登录表单提交成功")
                time.sleep(5)  # 等待处理
                return True
            else:
                logger.error("登录按钮点击失败")
                return False
            
        except Exception as e:
            logger.error(f"自动登录失败: {e}")
            return False
    
    def find_and_click_register_link(self):
        """查找并点击注册链接"""
        try:
            register_link_selectors = [
                "a[href*='register']",
                "a[href*='signup']",
                "a:contains('Register')",
                "a:contains('Sign up')",
                "a:contains('注册')"
            ]
            
            for selector in register_link_selectors:
                try:
                    if ":contains(" in selector:
                        text = selector.split("'")[1]
                        xpath = f"//a[contains(text(), '{text}')]"
                        link = self.driver.find_element(By.XPATH, xpath)
                    else:
                        link = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if link.is_displayed():
                        link.click()
                        logger.info(f"注册链接点击成功: {selector}")
                        time.sleep(3)
                        return True
                except:
                    continue
            
            logger.warning("未找到注册链接")
            return False
            
        except Exception as e:
            logger.error(f"查找注册链接失败: {e}")
            return False
    
    def click_register_button(self):
        """点击注册按钮"""
        return self._click_button([
            "button[type='submit']",
            "input[type='submit']",
            "//button[contains(text(), 'Register')]",
            "//button[contains(text(), 'Sign up')]",
            "//button[contains(text(), '注册')]",
            ".btn-primary",
            ".register-btn"
        ], "注册")
    
    def click_login_button(self):
        """点击登录按钮"""
        return self._click_button([
            "button[type='submit']",
            "input[type='submit']",
            "//button[contains(text(), 'Sign in')]",
            "//button[contains(text(), 'Login')]",
            "//button[contains(text(), '登录')]",
            ".btn-primary",
            ".login-btn"
        ], "登录")
    
    def _click_button(self, selectors, button_name):
        """通用按钮点击方法"""
        try:
            for selector in selectors:
                try:
                    if selector.startswith("//"):
                        button = self.driver.find_element(By.XPATH, selector)
                    else:
                        button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if button.is_displayed():
                        self.driver.execute_script("arguments[0].scrollIntoView();", button)
                        time.sleep(1)
                        button.click()
                        logger.info(f"{button_name}按钮点击成功: {selector}")
                        return True
                except:
                    continue
            
            logger.warning(f"未找到{button_name}按钮")
            return False
            
        except Exception as e:
            logger.error(f"点击{button_name}按钮失败: {e}")
            return False
    
    def wait_and_screenshot(self, filename="result.png", wait_time=10):
        """等待并截图"""
        try:
            logger.info(f"等待 {wait_time} 秒并截图...")
            time.sleep(wait_time)
            self.driver.save_screenshot(filename)
            logger.info(f"截图已保存: {filename}")
            return True
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("=== Augment Code 自动注册脚本 ===\n")
    
    # 检查配置
    if USER_CONFIG["email"] == "<EMAIL>":
        print("⚠️  请先修改 USER_CONFIG 中的用户信息！")
        print("请编辑脚本中的以下配置：")
        print("- email: 您的邮箱地址")
        print("- password: 您的密码")
        print("- username: 您的用户名（可选）")
        return
    
    # 检查Chrome
    if not os.path.exists(CHROME_PATH):
        print(f"❌ Chrome未找到: {CHROME_PATH}")
        return
    
    print(f"✅ Chrome找到: {CHROME_PATH}")
    print(f"✅ 邮箱: {USER_CONFIG['email']}")
    
    # 选择操作
    print("\n请选择操作：")
    print("1. 自动注册")
    print("2. 自动登录")
    print("3. 登录后访问订阅页面")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    bot = AutoRegisterBot(headless=False)
    
    try:
        if choice == "1":
            # 自动注册
            success = bot.auto_register(
                email=USER_CONFIG["email"],
                password=USER_CONFIG["password"],
                username=USER_CONFIG["username"],
                confirm_password=USER_CONFIG["confirm_password"]
            )
            
            if success:
                print("✅ 注册流程完成")
                bot.wait_and_screenshot("register_result.png", 10)
            else:
                print("❌ 注册失败")
                
        elif choice == "2":
            # 自动登录
            success = bot.auto_login(
                email=USER_CONFIG["email"],
                password=USER_CONFIG["password"]
            )
            
            if success:
                print("✅ 登录流程完成")
                bot.wait_and_screenshot("login_result.png", 10)
            else:
                print("❌ 登录失败")
                
        elif choice == "3":
            # 登录后访问订阅页面
            login_success = bot.auto_login(
                email=USER_CONFIG["email"],
                password=USER_CONFIG["password"]
            )
            
            if login_success:
                print("✅ 登录成功，正在访问订阅页面...")
                time.sleep(3)
                bot.navigate_to_page(URLS["subscription"])
                bot.wait_and_screenshot("subscription_page.png", 15)
                print("✅ 订阅页面访问完成")
            else:
                print("❌ 登录失败，无法访问订阅页面")
        
        else:
            print("❌ 无效选择")
            
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        
    finally:
        input("\n按回车键关闭浏览器...")
        bot.close()

if __name__ == "__main__":
    main()
