#!/usr/bin/env python3
"""
最终版本 - Augment Code 自动注册脚本
使用最智能的等待和检测机制处理人机验证
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 导入配置
from user_config import (
    USER_INFO, WEBSITE_URLS, BROWSER_CONFIG, 
    validate_config, print_config_status
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalAugmentBot:
    def __init__(self):
        """初始化最终版本机器人"""
        self.driver = None
        self.wait = None
        self.setup_driver()
    
    def setup_driver(self):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.binary_location = BROWSER_CONFIG["chrome_path"]
            chrome_options.add_argument("--incognito")
            chrome_options.add_argument("--new-window")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 添加更多反检测选项
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")
            chrome_options.add_argument("--disable-extensions")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 15)
            self.driver.maximize_window()
            
            logger.info("Chrome WebDriver 初始化成功")
            
        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def navigate_and_fill_email(self):
        """访问页面并填写邮箱"""
        try:
            logger.info("访问页面并填写邮箱...")
            self.driver.get(WEBSITE_URLS['subscription'])
            time.sleep(5)  # 增加等待时间
            
            # 等待并填写邮箱
            email_field = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "input[name='username']")))
            email_field.clear()
            
            # 模拟人类打字
            for char in USER_INFO["email"]:
                email_field.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            logger.info("邮箱填写完成")
            time.sleep(3)  # 等待页面响应
            return True
            
        except Exception as e:
            logger.error(f"访问页面或填写邮箱失败: {e}")
            return False
    
    def wait_for_verification_box(self, max_wait=60):
        """智能等待人机验证框出现"""
        try:
            logger.info(f"智能等待人机验证框出现，最多等待 {max_wait} 秒...")
            
            for i in range(max_wait):
                time.sleep(1)
                
                # 检查页面是否有变化
                try:
                    # 方法1: 检查是否出现了验证相关文本
                    page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()
                    if "verify" in page_text and "human" in page_text:
                        logger.info(f"检测到验证文本出现 ({i+1}秒)")
                        time.sleep(2)  # 再等待2秒让元素完全加载
                        return True
                    
                    # 方法2: 检查是否有复选框出现
                    checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
                    visible_checkboxes = [cb for cb in checkboxes if cb.is_displayed()]
                    if visible_checkboxes:
                        logger.info(f"检测到复选框出现 ({i+1}秒)")
                        return True
                    
                    # 方法3: 检查页面源码
                    if "verify you are human" in self.driver.page_source.lower():
                        logger.info(f"页面源码中检测到验证文本 ({i+1}秒)")
                        time.sleep(2)
                        return True
                    
                    if i % 10 == 0:  # 每10秒报告一次
                        logger.info(f"继续等待验证框... ({i+1}/{max_wait})")
                
                except Exception as e:
                    logger.debug(f"等待过程中的错误: {e}")
                    continue
            
            logger.warning("等待验证框超时")
            return False
            
        except Exception as e:
            logger.error(f"等待验证框失败: {e}")
            return False
    
    def handle_verification_with_multiple_strategies(self):
        """使用多种策略处理人机验证"""
        try:
            logger.info("=== 开始多策略人机验证处理 ===")
            
            # 策略1: 等待并查找标准复选框
            logger.info("策略1: 查找标准复选框")
            for i in range(10):
                try:
                    checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
                    for checkbox in checkboxes:
                        if checkbox.is_displayed() and not checkbox.is_selected():
                            logger.info("找到未选中的复选框，尝试点击")
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                            time.sleep(1)
                            checkbox.click()
                            time.sleep(2)
                            
                            if checkbox.is_selected():
                                logger.info("✅ 复选框点击成功！")
                                return True
                except:
                    pass
                
                time.sleep(1)
                logger.info(f"策略1重试 ({i+1}/10)")
            
            # 策略2: JavaScript强制操作
            logger.info("策略2: JavaScript强制操作")
            js_scripts = [
                # 脚本1: 查找并点击所有复选框
                """
                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                for (var i = 0; i < checkboxes.length; i++) {
                    var cb = checkboxes[i];
                    if (cb.offsetParent !== null && !cb.checked) {
                        cb.checked = true;
                        cb.click();
                        cb.dispatchEvent(new Event('change', {bubbles: true}));
                        cb.dispatchEvent(new Event('click', {bubbles: true}));
                        return 'success';
                    }
                }
                return 'no_checkbox_found';
                """,
                
                # 脚本2: 查找验证相关元素并点击
                """
                var elements = document.querySelectorAll('*');
                for (var i = 0; i < elements.length; i++) {
                    var el = elements[i];
                    if (el.textContent && el.textContent.toLowerCase().includes('verify') && 
                        el.textContent.toLowerCase().includes('human')) {
                        el.click();
                        return 'verify_area_clicked';
                    }
                }
                return 'no_verify_area_found';
                """,
                
                # 脚本3: 模拟点击坐标
                """
                var verifyElements = document.querySelectorAll('*');
                for (var i = 0; i < verifyElements.length; i++) {
                    var el = verifyElements[i];
                    if (el.textContent && el.textContent.includes('Verify you are human')) {
                        var rect = el.getBoundingClientRect();
                        var x = rect.left - 20; // 假设复选框在左侧
                        var y = rect.top + rect.height / 2;
                        
                        var event = new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true,
                            clientX: x,
                            clientY: y
                        });
                        
                        document.elementFromPoint(x, y).dispatchEvent(event);
                        return 'coordinate_click_attempted';
                    }
                }
                return 'no_verify_text_found';
                """
            ]
            
            for i, script in enumerate(js_scripts):
                try:
                    result = self.driver.execute_script(script)
                    logger.info(f"JavaScript脚本 {i+1} 执行结果: {result}")
                    time.sleep(2)
                    
                    # 检查是否成功
                    try:
                        checkbox = self.driver.find_element(By.CSS_SELECTOR, "input[type='checkbox']")
                        if checkbox.is_selected():
                            logger.info("✅ JavaScript操作成功！")
                            return True
                    except:
                        pass
                except Exception as e:
                    logger.info(f"JavaScript脚本 {i+1} 执行失败: {e}")
            
            # 策略3: 键盘导航
            logger.info("策略3: 键盘导航")
            try:
                body = self.driver.find_element(By.TAG_NAME, "body")
                body.click()  # 确保页面有焦点
                
                for i in range(20):  # 尝试Tab 20次
                    body.send_keys(Keys.TAB)
                    time.sleep(0.5)
                    
                    active_element = self.driver.switch_to.active_element
                    if (active_element.tag_name == "input" and 
                        active_element.get_attribute("type") == "checkbox"):
                        logger.info("通过Tab找到复选框")
                        active_element.send_keys(Keys.SPACE)
                        time.sleep(1)
                        
                        if active_element.is_selected():
                            logger.info("✅ 键盘操作成功！")
                            return True
            except Exception as e:
                logger.info(f"键盘导航失败: {e}")
            
            # 策略4: 等待用户手动操作
            logger.info("策略4: 等待用户手动操作")
            logger.info("🔔 请手动点击 'Verify you are human' 复选框")
            
            for i in range(30):  # 等待30秒
                time.sleep(1)
                try:
                    checkbox = self.driver.find_element(By.CSS_SELECTOR, "input[type='checkbox']")
                    if checkbox.is_selected():
                        logger.info("✅ 检测到用户已手动完成验证！")
                        return True
                except:
                    pass
                
                if i % 5 == 0:
                    logger.info(f"等待用户手动操作... ({i+1}/30)")
            
            logger.error("所有验证策略都失败了")
            return False
            
        except Exception as e:
            logger.error(f"多策略验证处理失败: {e}")
            return False
    
    def click_continue_button(self):
        """点击Continue按钮"""
        try:
            logger.info("尝试点击Continue按钮...")
            
            # 等待按钮可点击
            continue_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Continue')]"))
            )
            
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", continue_button)
            time.sleep(1)
            continue_button.click()
            
            logger.info("✅ Continue按钮点击成功")
            return True
            
        except Exception as e:
            logger.error(f"点击Continue按钮失败: {e}")
            return False
    
    def complete_registration_flow(self):
        """完成完整的注册流程"""
        try:
            logger.info("🚀 开始完整注册流程...")
            
            # 步骤1: 访问页面并填写邮箱
            if not self.navigate_and_fill_email():
                return False
            
            # 步骤2: 等待验证框出现
            if not self.wait_for_verification_box():
                logger.warning("验证框未出现，尝试继续...")
            
            # 步骤3: 处理人机验证
            if not self.handle_verification_with_multiple_strategies():
                logger.error("人机验证处理失败")
                return False
            
            # 步骤4: 点击Continue按钮
            if not self.click_continue_button():
                logger.error("Continue按钮点击失败")
                return False
            
            # 步骤5: 等待结果
            logger.info("等待页面跳转...")
            time.sleep(5)
            
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            logger.info(f"✅ 流程完成！")
            logger.info(f"当前页面: {page_title}")
            logger.info(f"当前URL: {current_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"注册流程失败: {e}")
            return False
    
    def take_screenshot(self, filename):
        """截取屏幕截图"""
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"截图已保存: {filename}")
        except Exception as e:
            logger.error(f"截图失败: {e}")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    print("=== 最终版本 - Augment Code 自动注册脚本 ===\n")
    
    if not print_config_status():
        print("\n请先修改 user_config.py 文件中的配置信息！")
        return
    
    print(f"\n目标邮箱: {USER_INFO['email']}")
    print(f"目标页面: {WEBSITE_URLS['subscription']}")
    print("\n这个脚本使用最智能的方法处理人机验证")
    print("包括：智能等待 → 多策略验证 → 自动/手动结合")
    
    confirm = input("\n是否开始最终注册流程？(y/n): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    bot = FinalAugmentBot()
    
    try:
        success = bot.complete_registration_flow()
        
        if success:
            print("🎉 注册流程完成！")
            bot.take_screenshot("final_success.png")
        else:
            print("❌ 注册流程失败")
            bot.take_screenshot("final_error.png")
        
        # 等待用户查看
        input("\n按回车键关闭浏览器...")
        
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        bot.take_screenshot("final_error.png")
        
    finally:
        bot.close()

if __name__ == "__main__":
    main()
