#!/usr/bin/env python3
"""
指纹浏览器管理器
基于比特浏览器API的完整管理系统
"""

import time
import json
import os
from bit_browser_api import BitBrowserAPI
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FingerprintBrowserManager:
    def __init__(self, website_name="MyProject"):
        """
        初始化指纹浏览器管理器
        
        Args:
            website_name: 项目名称，用于标识浏览器
        """
        self.api = BitBrowserAPI(website_name)
        self.active_browsers = {}  # 存储活跃的浏览器 {browser_id: driver}
        self.config_file = "browser_configs.json"
        self.load_configs()
    
    def load_configs(self):
        """加载浏览器配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.configs = json.load(f)
                logger.info(f"已加载 {len(self.configs)} 个浏览器配置")
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                self.configs = {}
        else:
            self.configs = {}
    
    def save_configs(self):
        """保存浏览器配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.configs, f, ensure_ascii=False, indent=2)
            logger.info("配置文件已保存")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def create_browser_profile(self, name, config):
        """
        创建浏览器配置文件
        
        Args:
            name: 浏览器名称
            config: 配置字典，包含以下字段：
                - username: 用户名
                - password: 密码
                - proxy: 代理配置 {'type': 'http', 'host': '', 'port': '', 'username': '', 'password': ''}
                - group: 分组名称
                - url: 启动URL
                - fingerprint: 自定义指纹参数
        """
        print(f"\n🔧 创建浏览器配置: {name}")
        
        # 提取配置参数
        username = config.get('username', '')
        password = config.get('password', '')
        proxy_config = config.get('proxy', None)
        group_name = config.get('group', 'default')
        start_url = config.get('url', '')
        
        # 自定义指纹参数
        fingerprint_params = config.get('fingerprint', {})
        
        # 创建浏览器
        browser_id = self.api.create_browser(
            name=name,
            username=username,
            password=password,
            proxy_config=proxy_config,
            url=start_url,
            **fingerprint_params
        )
        
        if browser_id:
            # 设置分组
            if group_name != 'default':
                self.api.update_browser_group(browser_id, group_name)
            
            # 保存配置
            self.configs[name] = {
                'browser_id': browser_id,
                'config': config,
                'created_time': time.time()
            }
            self.save_configs()
            
            print(f"✅ 浏览器配置创建成功: {name} (ID: {browser_id})")
            return browser_id
        else:
            print(f"❌ 浏览器配置创建失败: {name}")
            return None
    
    def open_browser(self, name):
        """打开指定名称的浏览器"""
        if name not in self.configs:
            print(f"❌ 未找到浏览器配置: {name}")
            return None
        
        browser_id = self.configs[name]['browser_id']
        
        # 检查是否已经打开
        if browser_id in self.active_browsers:
            print(f"⚠️ 浏览器已经打开: {name}")
            return self.active_browsers[browser_id]
        
        print(f"🚀 打开浏览器: {name}")
        driver = self.api.open_browser(browser_id)
        
        if driver:
            self.active_browsers[browser_id] = driver
            
            # 如果配置了启动URL，则导航到该URL
            start_url = self.configs[name]['config'].get('url', '')
            if start_url:
                try:
                    driver.get(start_url)
                    print(f"📄 已导航到: {start_url}")
                except Exception as e:
                    print(f"⚠️ 导航失败: {e}")
            
            print(f"✅ 浏览器打开成功: {name}")
            return driver
        else:
            print(f"❌ 浏览器打开失败: {name}")
            return None
    
    def close_browser(self, name):
        """关闭指定名称的浏览器"""
        if name not in self.configs:
            print(f"❌ 未找到浏览器配置: {name}")
            return False
        
        browser_id = self.configs[name]['browser_id']
        
        print(f"🔒 关闭浏览器: {name}")
        
        # 关闭WebDriver
        if browser_id in self.active_browsers:
            try:
                self.active_browsers[browser_id].quit()
            except:
                pass
            del self.active_browsers[browser_id]
        
        # 关闭浏览器进程
        result = self.api.close_browser(browser_id)
        
        if result:
            print(f"✅ 浏览器关闭成功: {name}")
        else:
            print(f"❌ 浏览器关闭失败: {name}")
        
        return result
    
    def delete_browser(self, name):
        """删除指定名称的浏览器"""
        if name not in self.configs:
            print(f"❌ 未找到浏览器配置: {name}")
            return False
        
        browser_id = self.configs[name]['browser_id']
        
        print(f"🗑️ 删除浏览器: {name}")
        
        # 先关闭浏览器
        self.close_browser(name)
        
        # 删除浏览器
        result = self.api.delete_browser(browser_id)
        
        if result:
            # 从配置中移除
            del self.configs[name]
            self.save_configs()
            print(f"✅ 浏览器删除成功: {name}")
        else:
            print(f"❌ 浏览器删除失败: {name}")
        
        return result
    
    def list_browsers(self):
        """列出所有浏览器配置"""
        print("\n📋 浏览器配置列表:")
        print("=" * 80)
        
        if not self.configs:
            print("暂无浏览器配置")
            return
        
        for name, info in self.configs.items():
            browser_id = info['browser_id']
            config = info['config']
            is_active = browser_id in self.active_browsers
            
            status = "🟢 运行中" if is_active else "⚪ 已停止"
            proxy_info = "无代理"
            if config.get('proxy'):
                proxy = config['proxy']
                proxy_info = f"{proxy['type']}://{proxy['host']}:{proxy['port']}"
            
            print(f"名称: {name}")
            print(f"  状态: {status}")
            print(f"  ID: {browser_id}")
            print(f"  用户名: {config.get('username', '未设置')}")
            print(f"  分组: {config.get('group', 'default')}")
            print(f"  代理: {proxy_info}")
            print(f"  启动URL: {config.get('url', '未设置')}")
            print("-" * 40)
    
    def close_all_browsers(self):
        """关闭所有浏览器"""
        print("\n🔒 关闭所有浏览器...")
        
        for name in list(self.configs.keys()):
            self.close_browser(name)
        
        print("✅ 所有浏览器已关闭")
    
    def arrange_windows(self, arrangement_type='box', columns=3):
        """排列窗口"""
        print(f"\n📐 排列窗口: {arrangement_type}")
        
        result = self.api.arrange_windows(
            arrangement_type=arrangement_type,
            start_x=0,
            start_y=0,
            width=600,
            height=800,
            columns=columns,
            space_x=10,
            space_y=10
        )
        
        if result:
            print("✅ 窗口排列成功")
        else:
            print("❌ 窗口排列失败")
        
        return result
    
    def get_browser_driver(self, name):
        """获取浏览器的WebDriver对象"""
        if name not in self.configs:
            return None
        
        browser_id = self.configs[name]['browser_id']
        return self.active_browsers.get(browser_id, None)
    
    def update_browser_proxy(self, name, proxy_config):
        """更新浏览器代理"""
        if name not in self.configs:
            print(f"❌ 未找到浏览器配置: {name}")
            return False
        
        browser_id = self.configs[name]['browser_id']
        
        print(f"🔄 更新浏览器代理: {name}")
        result = self.api.update_browser_proxy(browser_id, proxy_config)
        
        if result:
            # 更新本地配置
            self.configs[name]['config']['proxy'] = proxy_config
            self.save_configs()
            print(f"✅ 代理更新成功: {name}")
        else:
            print(f"❌ 代理更新失败: {name}")
        
        return result
