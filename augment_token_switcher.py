#!/usr/bin/env python3
"""
Augment Token 切换器
直接修改 VS Code 扩展配置来实现账号切换
"""

import json
import os
import shutil
import subprocess
import time
import winreg
from pathlib import Path
from typing import Dict, Optional
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AugmentTokenSwitcher:
    """Augment Token 切换器"""
    
    def __init__(self):
        self.vscode_user_dir = Path(os.path.expandvars(r"%APPDATA%\Code\User"))
        self.settings_file = self.vscode_user_dir / "settings.json"
        self.augment_storage = self.vscode_user_dir / "globalStorage" / "augment.vscode-augment"
        
    def get_vscode_settings(self) -> Dict:
        """获取 VS Code 设置"""
        if self.settings_file.exists():
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"读取 VS Code 设置失败: {e}")
        return {}
    
    def save_vscode_settings(self, settings: Dict):
        """保存 VS Code 设置"""
        try:
            # 确保目录存在
            self.settings_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            logger.info("VS Code 设置已更新")
        except Exception as e:
            logger.error(f"保存 VS Code 设置失败: {e}")
    
    def set_augment_token(self, access_token: str, tenant_url: str):
        """设置 Augment Token"""
        settings = self.get_vscode_settings()
        
        # 更新 Augment 相关设置
        augment_settings = {
            "augment.apiUrl": tenant_url,
            "augment.accessToken": access_token,
            "augment.tenantUrl": tenant_url
        }
        
        settings.update(augment_settings)
        self.save_vscode_settings(settings)
        
        # 同时设置环境变量
        os.environ["AUGMENT_ACCESS_TOKEN"] = access_token
        os.environ["AUGMENT_TENANT_URL"] = tenant_url
        
        # 设置系统环境变量（持久化）
        self.set_system_env_var("AUGMENT_ACCESS_TOKEN", access_token)
        self.set_system_env_var("AUGMENT_TENANT_URL", tenant_url)
    
    def set_system_env_var(self, name: str, value: str):
        """设置系统环境变量"""
        try:
            # 设置用户环境变量
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Environment", 0, winreg.KEY_SET_VALUE)
            winreg.SetValueEx(key, name, 0, winreg.REG_SZ, value)
            winreg.CloseKey(key)
            
            # 通知系统环境变量已更改
            subprocess.run([
                "powershell", "-Command", 
                f"[Environment]::SetEnvironmentVariable('{name}', '{value}', 'User')"
            ], check=False)
            
            logger.info(f"系统环境变量 {name} 已设置")
        except Exception as e:
            logger.error(f"设置系统环境变量失败: {e}")
    
    def clear_augment_cache(self):
        """清除 Augment 缓存"""
        try:
            if self.augment_storage.exists():
                # 备份当前配置
                backup_dir = Path("augment_cache_backup")
                backup_dir.mkdir(exist_ok=True)
                backup_path = backup_dir / f"cache_{int(time.time())}"
                shutil.copytree(self.augment_storage, backup_path)
                logger.info(f"缓存已备份到: {backup_path}")
                
                # 清除缓存
                shutil.rmtree(self.augment_storage)
                logger.info("Augment 缓存已清除")
        except Exception as e:
            logger.error(f"清除缓存失败: {e}")
    
    def restart_vscode_extension(self):
        """重启 VS Code 扩展"""
        try:
            # 方法1: 使用 VS Code 命令行重新加载窗口
            subprocess.run([
                "code", "--command", "workbench.action.reloadWindow"
            ], check=False)
            
            time.sleep(2)
            logger.info("VS Code 窗口已重新加载")
            return True
        except Exception as e:
            logger.error(f"重启扩展失败: {e}")
            return False
    
    def kill_vscode_processes(self):
        """终止所有 VS Code 进程"""
        try:
            # 终止 VS Code 主进程
            subprocess.run(["taskkill", "/f", "/im", "Code.exe"], 
                         capture_output=True, check=False)
            
            # 终止扩展宿主进程
            subprocess.run(["taskkill", "/f", "/im", "extensionHost.exe"], 
                         capture_output=True, check=False)
            
            time.sleep(2)
            logger.info("VS Code 进程已终止")
        except Exception as e:
            logger.error(f"终止进程失败: {e}")
    
    def start_vscode(self, workspace_path: str = "."):
        """启动 VS Code"""
        try:
            subprocess.Popen(["code", workspace_path], cwd=os.getcwd())
            time.sleep(3)
            logger.info("VS Code 已启动")
        except Exception as e:
            logger.error(f"启动 VS Code 失败: {e}")
    
    def switch_token_quick(self, access_token: str, tenant_url: str, 
                          clear_cache: bool = True, restart: bool = True):
        """快速切换 Token"""
        logger.info("开始切换 Augment Token...")
        
        # 1. 设置新的 Token
        self.set_augment_token(access_token, tenant_url)
        
        # 2. 清除缓存（可选）
        if clear_cache:
            self.clear_augment_cache()
        
        # 3. 重启 VS Code（可选）
        if restart:
            self.kill_vscode_processes()
            time.sleep(2)
            self.start_vscode()
        else:
            self.restart_vscode_extension()
        
        logger.info("Token 切换完成")
    
    def get_current_token_info(self) -> Dict:
        """获取当前 Token 信息"""
        settings = self.get_vscode_settings()
        
        return {
            "access_token": settings.get("augment.accessToken", "未设置"),
            "tenant_url": settings.get("augment.tenantUrl", settings.get("augment.apiUrl", "未设置")),
            "env_token": os.environ.get("AUGMENT_ACCESS_TOKEN", "未设置"),
            "env_tenant": os.environ.get("AUGMENT_TENANT_URL", "未设置")
        }
    
    def create_token_profile(self, name: str, access_token: str, tenant_url: str, 
                           email: str = "", description: str = ""):
        """创建 Token 配置文件"""
        profiles_dir = Path("augment_profiles")
        profiles_dir.mkdir(exist_ok=True)
        
        profile = {
            "name": name,
            "access_token": access_token,
            "tenant_url": tenant_url,
            "email": email,
            "description": description,
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        profile_file = profiles_dir / f"{name}.json"
        with open(profile_file, 'w', encoding='utf-8') as f:
            json.dump(profile, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Token 配置文件已创建: {profile_file}")
    
    def load_token_profile(self, name: str) -> Optional[Dict]:
        """加载 Token 配置文件"""
        profile_file = Path("augment_profiles") / f"{name}.json"
        if profile_file.exists():
            try:
                with open(profile_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
        return None
    
    def list_token_profiles(self) -> list:
        """列出所有 Token 配置文件"""
        profiles_dir = Path("augment_profiles")
        if not profiles_dir.exists():
            return []
        
        profiles = []
        for profile_file in profiles_dir.glob("*.json"):
            try:
                with open(profile_file, 'r', encoding='utf-8') as f:
                    profile = json.load(f)
                    profiles.append(profile)
            except Exception as e:
                logger.error(f"读取配置文件 {profile_file} 失败: {e}")
        
        return profiles
    
    def switch_to_profile(self, name: str, clear_cache: bool = True, restart: bool = True):
        """切换到指定配置文件"""
        profile = self.load_token_profile(name)
        if not profile:
            logger.error(f"配置文件不存在: {name}")
            return False
        
        logger.info(f"切换到配置: {name}")
        self.switch_token_quick(
            profile["access_token"], 
            profile["tenant_url"], 
            clear_cache, 
            restart
        )
        return True


def main():
    """命令行界面"""
    switcher = AugmentTokenSwitcher()
    
    while True:
        print("\n" + "="*50)
        print("🔄 Augment Token 切换器")
        print("="*50)
        print("1. 快速切换 Token")
        print("2. 创建 Token 配置")
        print("3. 切换到已保存配置")
        print("4. 列出所有配置")
        print("5. 查看当前 Token 信息")
        print("6. 清除 Augment 缓存")
        print("0. 退出")
        print("-"*50)
        
        choice = input("请选择操作 (0-6): ").strip()
        
        if choice == "1":
            print("\n⚡ 快速切换 Token")
            access_token = input("Access Token: ").strip()
            tenant_url = input("Tenant URL: ").strip()
            
            if access_token and tenant_url:
                clear_cache = input("是否清除缓存? (Y/n): ").strip().lower() != 'n'
                restart = input("是否重启 VS Code? (Y/n): ").strip().lower() != 'n'
                
                switcher.switch_token_quick(access_token, tenant_url, clear_cache, restart)
                print("✅ Token 切换完成")
            else:
                print("❌ Token 和 URL 不能为空")
        
        elif choice == "2":
            print("\n💾 创建 Token 配置")
            name = input("配置名称: ").strip()
            access_token = input("Access Token: ").strip()
            tenant_url = input("Tenant URL: ").strip()
            email = input("邮箱 (可选): ").strip()
            description = input("描述 (可选): ").strip()
            
            if name and access_token and tenant_url:
                switcher.create_token_profile(name, access_token, tenant_url, email, description)
                print("✅ 配置已保存")
            else:
                print("❌ 必填字段不能为空")
        
        elif choice == "3":
            print("\n🔄 切换到已保存配置")
            profiles = switcher.list_token_profiles()
            if not profiles:
                print("暂无保存的配置")
                continue
            
            print("可用配置:")
            for i, profile in enumerate(profiles, 1):
                print(f"{i}. {profile['name']} - {profile.get('email', 'N/A')}")
            
            try:
                idx = int(input("选择配置编号: ")) - 1
                if 0 <= idx < len(profiles):
                    profile_name = profiles[idx]["name"]
                    clear_cache = input("是否清除缓存? (Y/n): ").strip().lower() != 'n'
                    restart = input("是否重启 VS Code? (Y/n): ").strip().lower() != 'n'
                    
                    if switcher.switch_to_profile(profile_name, clear_cache, restart):
                        print("✅ 切换成功")
                    else:
                        print("❌ 切换失败")
                else:
                    print("❌ 无效的配置编号")
            except ValueError:
                print("❌ 请输入有效数字")
        
        elif choice == "4":
            print("\n📋 所有配置")
            profiles = switcher.list_token_profiles()
            if not profiles:
                print("暂无保存的配置")
            else:
                for i, profile in enumerate(profiles, 1):
                    print(f"{i}. {profile['name']}")
                    print(f"   邮箱: {profile.get('email', 'N/A')}")
                    print(f"   URL: {profile['tenant_url']}")
                    print(f"   描述: {profile.get('description', 'N/A')}")
                    print(f"   创建时间: {profile.get('created_at', 'N/A')}")
                    print()
        
        elif choice == "5":
            print("\n📊 当前 Token 信息")
            info = switcher.get_current_token_info()
            print(f"VS Code 设置中的 Token: {info['access_token'][:20]}..." if len(info['access_token']) > 20 else info['access_token'])
            print(f"VS Code 设置中的 URL: {info['tenant_url']}")
            print(f"环境变量中的 Token: {info['env_token'][:20]}..." if len(info['env_token']) > 20 else info['env_token'])
            print(f"环境变量中的 URL: {info['env_tenant']}")
        
        elif choice == "6":
            print("\n🧹 清除 Augment 缓存")
            confirm = input("确认清除缓存? (y/N): ").strip().lower()
            if confirm == 'y':
                switcher.clear_augment_cache()
                print("✅ 缓存已清除")
            else:
                print("操作已取消")
        
        elif choice == "0":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重试")


if __name__ == "__main__":
    main()
