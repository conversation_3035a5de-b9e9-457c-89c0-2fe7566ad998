#!/usr/bin/env python3
"""
Augment 账号快速切换示例
演示如何使用你提供的 Token 进行切换
"""

from augment_token_switcher import AugmentTokenSwitcher
from augment_account_pool import AugmentAccountPool

def quick_switch_demo():
    """快速切换演示"""
    print("🚀 Augment 账号快速切换演示")
    print("="*50)
    
    # 使用你提供的 Token 信息
    your_token = "aaabd8f0d4ab1233d05139a2f483f5a3ad33d3dc63586f5624b6aae7b8c0c77f"
    your_tenant_url = "https://d5.api.augmentcode.com/"
    
    # 创建切换器实例
    switcher = AugmentTokenSwitcher()
    
    print(f"准备切换到你的 Augment 账号...")
    print(f"Token: {your_token[:20]}...")
    print(f"Tenant URL: {your_tenant_url}")
    
    # 询问用户是否继续
    confirm = input("\n是否立即切换? (y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    try:
        # 执行切换
        print("\n正在切换账号...")
        switcher.switch_token_quick(
            access_token=your_token,
            tenant_url=your_tenant_url,
            clear_cache=True,  # 清除缓存确保干净切换
            restart=True       # 重启 VS Code
        )
        
        print("✅ 切换完成！VS Code 将会重启并使用新的 Augment 账号")
        
    except Exception as e:
        print(f"❌ 切换失败: {e}")

def setup_account_pool():
    """设置账号池"""
    print("\n📝 设置账号池")
    print("="*30)
    
    pool = AugmentAccountPool()
    
    # 添加你的账号到池中
    your_token = "aaabd8f0d4ab1233d05139a2f483f5a3ad33d3dc63586f5624b6aae7b8c0c77f"
    your_tenant_url = "https://d5.api.augmentcode.com/"
    
    pool.add_account(
        name="我的主账号",
        access_token=your_token,
        tenant_url=your_tenant_url,
        email="<EMAIL>",  # 请替换为实际邮箱
        description="主要使用的 Augment 账号"
    )
    
    print("✅ 账号已添加到池中")
    
    # 显示账号列表
    print("\n当前账号池:")
    accounts = pool.list_accounts()
    for i, acc in enumerate(accounts, 1):
        print(f"{i}. {acc['name']} - {acc.get('email', 'N/A')}")

def create_token_profiles():
    """创建 Token 配置文件"""
    print("\n💾 创建 Token 配置文件")
    print("="*35)
    
    switcher = AugmentTokenSwitcher()
    
    # 创建你的主账号配置
    your_token = "aaabd8f0d4ab1233d05139a2f483f5a3ad33d3dc63586f5624b6aae7b8c0c77f"
    your_tenant_url = "https://d5.api.augmentcode.com/"
    
    switcher.create_token_profile(
        name="main_account",
        access_token=your_token,
        tenant_url=your_tenant_url,
        email="<EMAIL>",  # 请替换为实际邮箱
        description="主要使用的 Augment 账号"
    )
    
    print("✅ Token 配置文件已创建")
    
    # 显示所有配置
    print("\n当前配置文件:")
    profiles = switcher.list_token_profiles()
    for i, profile in enumerate(profiles, 1):
        print(f"{i}. {profile['name']} - {profile.get('email', 'N/A')}")

def show_current_status():
    """显示当前状态"""
    print("\n📊 当前 Augment 状态")
    print("="*30)
    
    switcher = AugmentTokenSwitcher()
    info = switcher.get_current_token_info()
    
    print(f"VS Code 中的 Token: {info['access_token'][:20]}..." if len(info['access_token']) > 20 else info['access_token'])
    print(f"VS Code 中的 URL: {info['tenant_url']}")
    print(f"环境变量 Token: {info['env_token'][:20]}..." if len(info['env_token']) > 20 else info['env_token'])
    print(f"环境变量 URL: {info['env_tenant']}")

def main():
    """主菜单"""
    while True:
        print("\n" + "="*60)
        print("🎯 Augment 账号管理 - 快速切换示例")
        print("="*60)
        print("1. 🚀 立即切换到你的账号")
        print("2. 📝 设置账号池")
        print("3. 💾 创建 Token 配置文件")
        print("4. 📊 查看当前状态")
        print("5. 🔧 打开完整账号池管理")
        print("6. 🔄 打开 Token 切换器")
        print("0. 退出")
        print("-"*60)
        
        choice = input("请选择操作 (0-6): ").strip()
        
        if choice == "1":
            quick_switch_demo()
        
        elif choice == "2":
            setup_account_pool()
        
        elif choice == "3":
            create_token_profiles()
        
        elif choice == "4":
            show_current_status()
        
        elif choice == "5":
            print("\n启动完整账号池管理...")
            try:
                from augment_account_pool import main as pool_main
                pool_main()
            except ImportError:
                print("❌ 无法导入账号池管理模块")
        
        elif choice == "6":
            print("\n启动 Token 切换器...")
            try:
                from augment_token_switcher import main as switcher_main
                switcher_main()
            except ImportError:
                print("❌ 无法导入 Token 切换器模块")
        
        elif choice == "0":
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    print("🎉 欢迎使用 Augment 账号管理系统!")
    print("这个系统可以帮你轻松管理和切换多个 Augment 账号")
    print("\n主要功能:")
    print("• 使用 Access Token 直接切换账号")
    print("• 管理多个账号配置")
    print("• 自动重启 VS Code 应用新配置")
    print("• 备份和恢复配置")
    
    main()
