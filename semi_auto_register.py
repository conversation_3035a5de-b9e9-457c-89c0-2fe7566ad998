#!/usr/bin/env python3
"""
半自动注册脚本 - 最实用的解决方案
自动填写邮箱，提示用户手动点击验证，然后自动点击Continue
"""

import time
import random
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 导入配置
from user_config import (
    USER_INFO, WEBSITE_URLS, BROWSER_CONFIG, 
    validate_config, print_config_status
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SemiAutoRegisterBot:
    def __init__(self):
        """初始化半自动注册机器人"""
        self.driver = None
        self.wait = None
        self.setup_driver()
    
    def setup_driver(self):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.binary_location = BROWSER_CONFIG["chrome_path"]
            chrome_options.add_argument("--incognito")
            chrome_options.add_argument("--new-window")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 15)
            self.driver.maximize_window()
            
            logger.info("Chrome WebDriver 初始化成功")
            
        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            raise
    
    def navigate_and_fill_email(self):
        """访问页面并填写邮箱"""
        try:
            print("\n🌐 正在访问 Augment Code 订阅页面...")
            self.driver.get(WEBSITE_URLS['subscription'])
            time.sleep(5)
            
            print("📧 正在自动填写邮箱地址...")
            email_field = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "input[name='username']")))
            email_field.clear()
            
            # 模拟人类打字
            for char in USER_INFO["email"]:
                email_field.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            print(f"✅ 邮箱填写完成: {USER_INFO['email']}")
            time.sleep(3)
            return True
            
        except Exception as e:
            print(f"❌ 访问页面或填写邮箱失败: {e}")
            return False
    
    def wait_for_user_verification(self):
        """等待用户手动完成验证"""
        try:
            print("\n" + "="*60)
            print("🔔 请注意：需要您手动完成人机验证！")
            print("="*60)
            print("📋 操作步骤：")
            print("   1. 在浏览器中找到 'Verify you are human' 复选框")
            print("   2. 手动点击该复选框进行验证")
            print("   3. 等待验证完成（复选框变为选中状态）")
            print("   4. 验证完成后，脚本将自动继续")
            print("="*60)
            
            # 智能检测用户是否完成验证
            max_wait = 120  # 最多等待2分钟
            for i in range(max_wait):
                time.sleep(1)
                
                try:
                    # 检查复选框是否被选中
                    checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
                    for checkbox in checkboxes:
                        if checkbox.is_displayed() and checkbox.is_selected():
                            print("✅ 检测到验证已完成！")
                            return True
                    
                    # 检查Continue按钮是否可用
                    try:
                        continue_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Continue')]")
                        if continue_button.is_enabled():
                            print("✅ 检测到Continue按钮已可用！")
                            return True
                    except:
                        pass
                    
                    # 每10秒提示一次
                    if i % 10 == 0 and i > 0:
                        remaining = max_wait - i
                        print(f"⏳ 等待用户完成验证... (剩余 {remaining} 秒)")
                        print("   💡 提示：请在浏览器中点击 'Verify you are human' 复选框")
                
                except Exception as e:
                    logger.debug(f"检测过程中的错误: {e}")
                    continue
            
            print("⏰ 等待超时，请检查是否已完成验证")
            return False
            
        except Exception as e:
            print(f"❌ 等待用户验证失败: {e}")
            return False
    
    def auto_click_continue(self):
        """自动点击Continue按钮"""
        try:
            print("\n🔘 正在自动点击 Continue 按钮...")
            
            # 等待按钮可点击
            continue_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Continue')]"))
            )
            
            # 滚动到按钮位置
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", continue_button)
            time.sleep(1)
            
            # 点击按钮
            continue_button.click()
            print("✅ Continue 按钮点击成功！")
            
            # 等待页面跳转
            print("⏳ 等待页面跳转...")
            time.sleep(5)
            
            return True
            
        except Exception as e:
            print(f"❌ 点击 Continue 按钮失败: {e}")
            return False
    
    def check_final_result(self):
        """检查最终结果"""
        try:
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            print("\n" + "="*60)
            print("📊 注册流程结果")
            print("="*60)
            print(f"📄 当前页面标题: {page_title}")
            print(f"🔗 当前页面URL: {current_url}")
            
            # 判断结果
            if "password" in current_url.lower() or "sign" in page_title.lower():
                print("🎉 成功进入下一步！可能需要设置密码或已进入登录流程")
                return True
            elif "dashboard" in current_url.lower() or "subscription" in current_url.lower():
                print("🎉 可能已经成功登录或注册！")
                return True
            else:
                print("ℹ️ 流程已完成，请查看当前页面状态")
                return True
            
        except Exception as e:
            print(f"❌ 检查结果失败: {e}")
            return False
    
    def complete_semi_auto_flow(self):
        """完成半自动注册流程"""
        try:
            print("🚀 开始半自动注册流程...")
            print("📝 流程说明：自动填写邮箱 → 用户手动验证 → 自动点击Continue")
            
            # 步骤1: 自动访问页面并填写邮箱
            if not self.navigate_and_fill_email():
                return False
            
            # 步骤2: 等待用户手动完成验证
            if not self.wait_for_user_verification():
                print("❌ 用户验证超时或失败")
                return False
            
            # 步骤3: 自动点击Continue按钮
            if not self.auto_click_continue():
                print("❌ 自动点击Continue失败")
                return False
            
            # 步骤4: 检查最终结果
            self.check_final_result()
            
            return True
            
        except Exception as e:
            print(f"❌ 半自动注册流程失败: {e}")
            return False
    
    def take_screenshot(self, filename):
        """截取屏幕截图"""
        try:
            self.driver.save_screenshot(filename)
            print(f"📸 截图已保存: {filename}")
        except Exception as e:
            print(f"❌ 截图失败: {e}")
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("🔒 浏览器已关闭")

def main():
    """主函数"""
    print("="*70)
    print("🤖 半自动 Augment Code 注册脚本")
    print("="*70)
    print("📋 功能说明：")
    print("   ✅ 自动访问页面")
    print("   ✅ 自动填写邮箱地址")
    print("   👆 用户手动点击验证复选框")
    print("   ✅ 自动点击Continue按钮")
    print("   ✅ 自动检查结果")
    print("="*70)
    
    # 检查配置
    if not print_config_status():
        print("\n❌ 请先修改 user_config.py 文件中的配置信息！")
        return
    
    print(f"\n📧 目标邮箱: {USER_INFO['email']}")
    print(f"🌐 目标页面: {WEBSITE_URLS['subscription']}")
    
    confirm = input("\n❓ 是否开始半自动注册流程？(y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    bot = SemiAutoRegisterBot()
    
    try:
        success = bot.complete_semi_auto_flow()
        
        if success:
            print("\n🎉 半自动注册流程完成！")
            bot.take_screenshot("semi_auto_success.png")
        else:
            print("\n❌ 半自动注册流程失败")
            bot.take_screenshot("semi_auto_error.png")
        
        print("\n📸 操作截图已保存，您可以查看详细结果")
        
        # 等待用户查看结果
        input("\n⏸️ 按回车键关闭浏览器...")
        
    except Exception as e:
        print(f"\n❌ 执行过程中出错: {e}")
        bot.take_screenshot("semi_auto_error.png")
        
    finally:
        bot.close()

if __name__ == "__main__":
    main()
