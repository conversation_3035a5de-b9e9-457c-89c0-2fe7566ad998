#!/usr/bin/env python3
"""
快速测试脚本
"""

from registration_automation import RegistrationBot

def main():
    print("快速测试WebDriver初始化...")
    
    try:
        bot = RegistrationBot(headless=True, wait_timeout=5)
        print("✅ WebDriver初始化成功！")
        
        # 测试访问网页
        bot.driver.get("https://www.google.com")
        title = bot.driver.title
        print(f"✅ 网页访问成功！标题: {title}")
        
        bot.close()
        print("✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
