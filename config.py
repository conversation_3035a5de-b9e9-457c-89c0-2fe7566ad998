"""
配置文件
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 网站配置
REGISTRATION_URL = "https://app.augmentcode.com/account/subscription"  # 需要根据实际情况调整
LOGIN_URL = "https://app.augmentcode.com/login"

# 浏览器配置
BROWSER = 'auto'  # 浏览器类型 ('chrome', 'edge', 'auto')
HEADLESS_MODE = False  # 是否使用无头模式
WAIT_TIMEOUT = 10  # 等待超时时间（秒）
PAGE_LOAD_TIMEOUT = 30  # 页面加载超时时间（秒）

# Chrome浏览器路径（如果Chrome安装在非标准位置）
CHROME_BINARY_PATH = r"E:\Google\Chrome\Application\chrome.exe"

# 延迟配置（模拟人类行为）
MIN_DELAY = 1  # 最小延迟（秒）
MAX_DELAY = 3  # 最大延迟（秒）
TYPING_DELAY_MIN = 0.05  # 打字最小延迟（秒）
TYPING_DELAY_MAX = 0.15  # 打字最大延迟（秒）

# 重试配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 5  # 重试延迟（秒）

# 日志配置
LOG_LEVEL = "INFO"
LOG_FILE = "registration.log"

# 用户代理
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
]

# 从环境变量获取敏感信息
EMAIL = os.getenv('REGISTRATION_EMAIL', '')
PASSWORD = os.getenv('REGISTRATION_PASSWORD', '')
USERNAME = os.getenv('REGISTRATION_USERNAME', '')

# 表单选择器配置（根据实际网站调整）
SELECTORS = {
    'email': [
        "input[type='email']",
        "input[name='email']",
        "input[id='email']",
        "#email",
        ".email-input"
    ],
    'password': [
        "input[type='password']",
        "input[name='password']",
        "input[id='password']",
        "#password",
        ".password-input"
    ],
    'confirm_password': [
        "input[name='confirm_password']",
        "input[name='password_confirmation']",
        "input[id='confirm_password']",
        "#confirm_password",
        ".confirm-password-input"
    ],
    'username': [
        "input[name='username']",
        "input[id='username']",
        "#username",
        ".username-input"
    ],
    'submit_button': [
        "button[type='submit']",
        "input[type='submit']",
        "button[class*='submit']",
        "button[class*='register']",
        ".submit-btn",
        ".register-btn",
        ".btn-primary"
    ],
    'captcha': [
        ".captcha",
        "#captcha",
        ".recaptcha",
        ".hcaptcha"
    ]
}

# 成功指示器
SUCCESS_INDICATORS = [
    "//div[contains(text(), 'success')]",
    "//div[contains(text(), '成功')]",
    "//div[contains(text(), 'welcome')]",
    "//div[contains(text(), '欢迎')]",
    "//div[contains(text(), 'registered')]",
    "//div[contains(text(), '注册')]",
    ".success-message",
    ".alert-success"
]

# 错误指示器
ERROR_INDICATORS = [
    "//div[contains(text(), 'error')]",
    "//div[contains(text(), '错误')]",
    "//div[contains(text(), 'failed')]",
    "//div[contains(text(), '失败')]",
    ".error-message",
    ".alert-error",
    ".alert-danger"
]
